cmake_minimum_required(VERSION 3.16)

project(QTFirewall VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required Qt components
find_package(Qt6 REQUIRED COMPONENTS
    Core
    Widgets
    Network
    Charts # Re-added Charts here
    Svg
    Concurrent
    PrintSupport
)

# Enable Qt MOC, UIC, and RCC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Source files
set(SOURCES
    # Main application
    src/main.cpp
    src/core/application.cpp
    src/core/applicationcontroller.cpp

    # Data Models
    src/models/packetmodel.cpp
    src/models/rulemodel.cpp
    src/models/alertmodel.cpp
    src/models/networkinterfacemodel.cpp
    src/models/configurationmodel.cpp

    # Network Layer
    src/network/packetcapture.cpp
    src/network/protocolanalyzer.cpp
    src/network/ipanalyzer.cpp
    src/network/arpanalyzer.cpp
    src/network/tcpanalyzer.cpp
    src/network/udpanalyzer.cpp
    src/network/firewallengine.cpp

    # UI Components
    src/ui/mainwindow.cpp
    src/ui/welcomewizard.cpp
    src/ui/dashboardview.cpp
    src/ui/rulemanagerview.cpp
    src/ui/alertview.cpp
    src/ui/reportview.cpp
    src/ui/settingsview.cpp

    # Custom Widgets
    src/ui/widgets/networkvisualizationwidget.cpp
    src/ui/widgets/packetflowwidget.cpp
    src/ui/widgets/rulebuilderwidget.cpp
    src/ui/widgets/alertpanelwidget.cpp
    src/ui/widgets/statisticswidget.cpp
    src/ui/widgets/animatedbutton.cpp
    src/ui/widgets/protocolfilterwidget.cpp

    # Dialogs
    src/ui/dialogs/ruledialog.cpp
    src/ui/dialogs/settingsdialog.cpp
    src/ui/dialogs/exportdialog.cpp
    src/ui/dialogs/aboutdialog.cpp

    # Utilities
    src/utils/logger.cpp
    src/utils/configmanager.cpp
    src/utils/exportmanager.cpp
    src/utils/animationhelper.cpp
    src/utils/thememanager.cpp
)
# Header files
set(HEADERS
    # Core
    src/core/application.h
    src/core/applicationcontroller.h

    # Models
    src/models/packetmodel.h
    src/models/rulemodel.h
    src/models/alertmodel.h
    src/models/networkinterfacemodel.h
    src/models/configurationmodel.h

    # Network
    src/network/packetcapture.h
    src/network/protocolanalyzer.h
    src/network/ipanalyzer.h
    src/network/arpanalyzer.h
    src/network/tcpanalyzer.h
    src/network/udpanalyzer.h
    src/network/firewallengine.h

    # UI
    src/ui/mainwindow.h
    src/ui/welcomewizard.h
    src/ui/dashboardview.h
    src/ui/rulemanagerview.h
    src/ui/alertview.h
    src/ui/reportview.h
    src/ui/settingsview.h

    # Widgets
    src/ui/widgets/networkvisualizationwidget.h
    src/ui/widgets/packetflowwidget.h
    src/ui/widgets/rulebuilderwidget.h
    src/ui/widgets/alertpanelwidget.h
    src/ui/widgets/statisticswidget.h
    src/ui/widgets/animatedbutton.h
    src/ui/widgets/protocolfilterwidget.h

    # Dialogs
    src/ui/dialogs/ruledialog.h
    src/ui/dialogs/settingsdialog.h
    src/ui/dialogs/exportdialog.h
    src/ui/dialogs/aboutdialog.h

    # Utils
    src/utils/logger.h
    src/utils/configmanager.h
    src/utils/exportmanager.h
    src/utils/animationhelper.h
    src/utils/thememanager.h
)

# UI files - using programmatic UI creation instead of .ui files
# set(UI_FILES)

# Resource files
set(RESOURCE_FILES
    resources/resources.qrc
)

# Create executable
add_executable(QTFirewall
    ${SOURCES}
    ${HEADERS}
    ${RESOURCE_FILES}
)

# Include directories
# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${CMAKE_CURRENT_SOURCE_DIR}/src/core
    ${CMAKE_CURRENT_SOURCE_DIR}/src/models
    ${CMAKE_CURRENT_SOURCE_DIR}/src/network
    ${CMAKE_CURRENT_SOURCE_DIR}/src/ui
    ${CMAKE_CURRENT_SOURCE_DIR}/src/ui/widgets
    ${CMAKE_CURRENT_SOURCE_DIR}/src/ui/dialogs
    ${CMAKE_CURRENT_SOURCE_DIR}/src/ui/views
    ${CMAKE_CURRENT_SOURCE_DIR}/src/utils
)

# Link Qt libraries
target_link_libraries(QTFirewall
    Qt6::Core
    Qt6::Widgets
    Qt6::Network
    Qt6::Charts
    Qt6::Svg
    Qt6::Concurrent
    Qt6::PrintSupport
)

# Platform-specific libraries
if(WIN32)
    target_link_libraries(QTFirewall ws2_32 iphlpapi)
elseif(UNIX AND NOT APPLE)
    target_link_libraries(QTFirewall pcap)
elseif(APPLE)
    target_link_libraries(QTFirewall pcap)
endif()

# Compiler-specific options
if(MSVC)
    target_compile_options(QTFirewall PRIVATE /W4)
else()
    target_compile_options(QTFirewall PRIVATE -Wall -Wextra -Wpedantic)
endif()

# Install rules
install(TARGETS QTFirewall
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

# Copy Qt runtime libraries on Windows
if(WIN32)
    find_program(WINDEPLOYQT_EXECUTABLE windeployqt HINTS ${Qt6_DIR}/../../../bin)
    if(WINDEPLOYQT_EXECUTABLE)
        add_custom_command(TARGET QTFirewall POST_BUILD
            COMMAND ${WINDEPLOYQT_EXECUTABLE} $<TARGET_FILE:QTFirewall>
            COMMENT "Deploying Qt libraries")
    endif()
endif()
