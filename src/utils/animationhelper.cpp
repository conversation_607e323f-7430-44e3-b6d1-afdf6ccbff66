#include "animationhelper.h"
#include <QPropertyAnimation>
#include <QParallelAnimationGroup>
#include <QSequentialAnimationGroup>
#include <QEasingCurve>

AnimationHelper::AnimationHelper(QObject *parent)
    : QObject(parent)
{
}

AnimationHelper::~AnimationHelper() = default;

// Static animation creation methods
QPropertyAnimation* AnimationHelper::createFadeAnimation(QWidget *widget, int duration, qreal startOpacity, qreal endOpacity)
{
    QPropertyAnimation *animation = new QPropertyAnimation(widget, "windowOpacity");
    animation->setDuration(duration);
    animation->setStartValue(startOpacity);
    animation->setEndValue(endOpacity);
    animation->setEasingCurve(QEasingCurve::InOutQuad);
    return animation;
}

QPropertyAnimation* AnimationHelper::createSlideAnimation(QWidget *widget, int duration, const QPoint &startPos, const QPoint &endPos)
{
    QPropertyAnimation *animation = new QPropertyAnimation(widget, "pos");
    animation->setDuration(duration);
    animation->setStartValue(startPos);
    animation->setEndValue(endPos);
    animation->setEasingCurve(QEasingCurve::OutCubic);
    return animation;
}

QPropertyAnimation* AnimationHelper::createScaleAnimation(QWidget *widget, int duration, qreal startScale, qreal endScale)
{
    // Note: This is a simplified implementation
    // In a real implementation, you'd need to handle scaling differently
    QPropertyAnimation *animation = new QPropertyAnimation(widget, "geometry");
    animation->setDuration(duration);
    animation->setEasingCurve(QEasingCurve::OutBack);
    return animation;
}

QPropertyAnimation* AnimationHelper::createRotationAnimation(QWidget *widget, int duration, qreal startAngle, qreal endAngle)
{
    Q_UNUSED(widget)
    Q_UNUSED(startAngle)
    Q_UNUSED(endAngle)
    // Simplified implementation - rotation requires graphics effects
    QPropertyAnimation *animation = new QPropertyAnimation();
    animation->setDuration(duration);
    animation->setEasingCurve(QEasingCurve::InOutQuad);
    return animation;
}

QPropertyAnimation* AnimationHelper::createColorAnimation(QWidget *widget, int duration, const QColor &startColor, const QColor &endColor)
{
    Q_UNUSED(widget)
    Q_UNUSED(startColor)
    Q_UNUSED(endColor)
    // Simplified implementation
    QPropertyAnimation *animation = new QPropertyAnimation();
    animation->setDuration(duration);
    animation->setEasingCurve(QEasingCurve::InOutQuad);
    return animation;
}

QParallelAnimationGroup* AnimationHelper::createParallelGroup(const QList<QAbstractAnimation*> &animations)
{
    QParallelAnimationGroup *group = new QParallelAnimationGroup();
    for (QAbstractAnimation *animation : animations) {
        group->addAnimation(animation);
    }
    return group;
}

QSequentialAnimationGroup* AnimationHelper::createSequentialGroup(const QList<QAbstractAnimation*> &animations)
{
    QSequentialAnimationGroup *group = new QSequentialAnimationGroup();
    for (QAbstractAnimation *animation : animations) {
        group->addAnimation(animation);
    }
    return group;
}

// Convenience methods
void AnimationHelper::fadeIn(QWidget *widget, int duration)
{
    QPropertyAnimation *animation = createFadeAnimation(widget, duration, 0.0, 1.0);
    animation->start(QAbstractAnimation::DeleteWhenStopped);
}

void AnimationHelper::fadeOut(QWidget *widget, int duration)
{
    QPropertyAnimation *animation = createFadeAnimation(widget, duration, 1.0, 0.0);
    animation->start(QAbstractAnimation::DeleteWhenStopped);
}

void AnimationHelper::slideIn(QWidget *widget, SlideDirection direction, int duration)
{
    QPoint startPos, endPos = widget->pos();

    switch (direction) {
    case SlideFromLeft:
        startPos = QPoint(-widget->width(), endPos.y());
        break;
    case SlideFromRight:
        startPos = QPoint(widget->parentWidget()->width(), endPos.y());
        break;
    case SlideFromTop:
        startPos = QPoint(endPos.x(), -widget->height());
        break;
    case SlideFromBottom:
        startPos = QPoint(endPos.x(), widget->parentWidget()->height());
        break;
    }

    QPropertyAnimation *animation = createSlideAnimation(widget, duration, startPos, endPos);
    animation->start(QAbstractAnimation::DeleteWhenStopped);
}

void AnimationHelper::slideOut(QWidget *widget, SlideDirection direction, int duration)
{
    QPoint startPos = widget->pos(), endPos;

    switch (direction) {
    case SlideFromLeft:
        endPos = QPoint(-widget->width(), startPos.y());
        break;
    case SlideFromRight:
        endPos = QPoint(widget->parentWidget()->width(), startPos.y());
        break;
    case SlideFromTop:
        endPos = QPoint(startPos.x(), -widget->height());
        break;
    case SlideFromBottom:
        endPos = QPoint(startPos.x(), widget->parentWidget()->height());
        break;
    }

    QPropertyAnimation *animation = createSlideAnimation(widget, duration, startPos, endPos);
    animation->start(QAbstractAnimation::DeleteWhenStopped);
}
