#include "logger.h"
#include <QStandardPaths>
#include <QDir>
#include <QMutexLocker>
#include <QCoreApplication>
#include <iostream>

Logger* Logger::s_instance = nullptr;

void Logger::initialize(LogLevel level)
{
    if (!s_instance) {
        s_instance = new Logger();
        s_instance->setLogLevel(level);

        // Set up default log file
        QString logDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
        QDir().mkpath(logDir);
        QString logFile = logDir + "/qtfirewall.log";
        s_instance->setLogFile(logFile);
        s_instance->enableConsoleOutput(true);

        s_instance->info("Logger initialized", "Logger");
    }
}

void Logger::cleanup()
{
    if (s_instance) {
        s_instance->info("Logger shutting down", "Logger");
        delete s_instance;
        s_instance = nullptr;
    }
}

Logger& Logger::instance()
{
    if (!s_instance) {
        initialize();
    }
    return *s_instance;
}

Logger::Logger(QObject *parent)
    : QObject(parent)
    , m_logLevel(INFO)
    , m_logFile(nullptr)
    , m_logStream(nullptr)
    , m_consoleOutput(true)
{
}

Logger::~Logger()
{
    if (m_logStream) {
        delete m_logStream;
    }
    if (m_logFile) {
        m_logFile->close();
        delete m_logFile;
    }
}

void Logger::setLogLevel(LogLevel level)
{
    QMutexLocker locker(&m_mutex);
    m_logLevel = level;
}

void Logger::setLogFile(const QString &filename)
{
    QMutexLocker locker(&m_mutex);

    if (m_logStream) {
        delete m_logStream;
        m_logStream = nullptr;
    }
    if (m_logFile) {
        m_logFile->close();
        delete m_logFile;
        m_logFile = nullptr;
    }

    if (!filename.isEmpty()) {
        m_logFile = new QFile(filename);
        if (m_logFile->open(QIODevice::WriteOnly | QIODevice::Append)) {
            m_logStream = new QTextStream(m_logFile);
        } else {
            delete m_logFile;
            m_logFile = nullptr;
        }
    }
}

void Logger::enableConsoleOutput(bool enabled)
{
    QMutexLocker locker(&m_mutex);
    m_consoleOutput = enabled;
}

void Logger::log(LogLevel level, const QString &message, const QString &category)
{
    if (level < m_logLevel) {
        return;
    }

    QMutexLocker locker(&m_mutex);
    QString formattedMessage = formatMessage(level, message, category);

    // Write to file
    if (m_logStream) {
        *m_logStream << formattedMessage << Qt::endl;
        m_logStream->flush();
    }

    // Write to console
    if (m_consoleOutput) {
        if (level >= ERROR) {
            std::cerr << formattedMessage.toStdString() << std::endl;
        } else {
            std::cout << formattedMessage.toStdString() << std::endl;
        }
    }
}

void Logger::debug(const QString &message, const QString &category)
{
    log(DEBUG, message, category);
}

void Logger::info(const QString &message, const QString &category)
{
    log(INFO, message, category);
}

void Logger::warning(const QString &message, const QString &category)
{
    log(WARNING, message, category);
}

void Logger::error(const QString &message, const QString &category)
{
    log(ERROR, message, category);
}

void Logger::critical(const QString &message, const QString &category)
{
    log(CRITICAL, message, category);
}

QString Logger::levelToString(LogLevel level) const
{
    switch (level) {
        case DEBUG: return "DEBUG";
        case INFO: return "INFO";
        case WARNING: return "WARN";
        case ERROR: return "ERROR";
        case CRITICAL: return "CRIT";
        default: return "UNKNOWN";
    }
}

QString Logger::formatMessage(LogLevel level, const QString &message, const QString &category) const
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    QString levelStr = levelToString(level);
    QString categoryStr = category.isEmpty() ? "" : QString("[%1] ").arg(category);

    return QString("%1 [%2] %3%4")
           .arg(timestamp)
           .arg(levelStr)
           .arg(categoryStr)
           .arg(message);
}