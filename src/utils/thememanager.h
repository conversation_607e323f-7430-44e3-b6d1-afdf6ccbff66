#ifndef THEMEMANAGER_H
#define THEMEMANAGER_H

#include <QObject>
#include <QString>

/**
 * @brief Theme management singleton for the QT Firewall application
 */
class ThemeManager : public QObject
{
    Q_OBJECT

public:
    static ThemeManager& instance();

    void initialize();
    void applyTheme(const QString &themeName);

    QStringList getAvailableThemes() const;
    QString getCurrentTheme() const;

signals:
    void themeChanged(const QString &themeName);

private:
    explicit ThemeManager(QObject *parent = nullptr);
    ~ThemeManager();

    QString loadThemeStylesheet(const QString &themeName);

    static ThemeManager *s_instance;
    QString m_currentTheme;
};

#endif // THEMEMANAGER_H
