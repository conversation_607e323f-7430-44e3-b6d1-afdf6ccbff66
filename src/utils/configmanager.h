#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

#include <QObject>
#include <QSettings>
#include <QVariant>
#include <QMutex>

/**
 * @brief Configuration management singleton for the QT Firewall application
 */
class ConfigManager : public QObject
{
    Q_OBJECT

public:
    static ConfigManager& instance();

    bool initialize(const QString &configFile = QString());

    QVariant getValue(const QString &key, const QVariant &defaultValue = QVariant()) const;
    void setValue(const QString &key, const QVariant &value);

    void sync();
    void reset();

private:
    explicit ConfigManager(QObject *parent = nullptr);
    ~ConfigManager();

    void setDefaultValues();

    static ConfigManager *s_instance;
    QSettings *m_settings;
    mutable QMutex m_mutex;
};

#endif // CONFIGMANAGER_H
