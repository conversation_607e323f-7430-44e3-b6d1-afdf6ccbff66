#ifndef EXPORTMANAGER_H
#define EXPORTMANAGER_H

#include <QObject>
#include <QThread>
#include <QMutex>
#include <QTimer>
#include <QDateTime>
#include <QJsonObject>
#include <QJsonDocument>
#include <QJsonArray>
#include <QXmlStreamWriter>
#include <QTextStream>
#include <QFileInfo>
#include <QDir>
#include "../models/packetmodel.h"
#include "../models/rulemodel.h"
#include "../models/alertmodel.h"
#include "../ui/dialogs/exportdialog.h"

class QTextDocument;
class QPrinter;

/**
 * @brief Export functionality manager with support for multiple formats
 *
 * This class provides comprehensive export functionality for all application data
 * with support for multiple formats and advanced features:
 * - Multiple export formats (CSV, JSON, XML, PDF, HTML, Excel, PowerPoint, Text)
 * - Asynchronous export operations with progress tracking
 * - Template-based exports with customizable layouts
 * - Data filtering and transformation
 * - Batch export operations
 * - Scheduled exports with automation
 * - Export validation and error handling
 * - Memory-efficient streaming for large datasets
 */
class ExportManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Export operation status
     */
    enum ExportStatus {
        IDLE = 0,           ///< No export operation in progress
        PREPARING,          ///< Preparing data for export
        EXPORTING,          ///< Export operation in progress
        FINALIZING,         ///< Finalizing export file
        COMPLETED,          ///< Export completed successfully
        CANCELLED,          ///< Export was cancelled
        ERROR               ///< Export failed with error
    };

    /**
     * @brief Export configuration structure
     */
    struct ExportConfig {
        ExportFormat format;            ///< Export format
        ExportDataType dataType;        ///< Type of data to export
        QString outputPath;             ///< Output file path
        QDateTime startDate;            ///< Start date for filtering
        QDateTime endDate;              ///< End date for filtering
        QStringList includeFields;      ///< Fields to include in export
        bool compressionEnabled;        ///< Whether to compress output
        bool includeTimestamps;         ///< Whether to include timestamps
        int maxRecords;                 ///< Maximum number of records
        QString templateName;           ///< Template name (if applicable)
        QVariantMap customOptions;      ///< Custom format-specific options

        ExportConfig() : format(ExportFormat::CSV), dataType(ExportDataType::PACKETS),
                        compressionEnabled(false), includeTimestamps(true), maxRecords(-1) {}
    };

    explicit ExportManager(QObject *parent = nullptr);
    ~ExportManager();

    // Data source management
    void setPacketModel(PacketModel *model) { m_packetModel = model; }
    void setRuleModel(RuleModel *model) { m_ruleModel = model; }
    void setAlertModel(AlertModel *model) { m_alertModel = model; }

    PacketModel* getPacketModel() const { return m_packetModel; }
    RuleModel* getRuleModel() const { return m_ruleModel; }
    AlertModel* getAlertModel() const { return m_alertModel; }

    // Export operations
    bool startExport(const ExportConfig &config);
    void cancelExport();
    bool isExporting() const { return m_status != IDLE && m_status != COMPLETED && m_status != ERROR; }
    ExportStatus getStatus() const { return m_status; }
    QString getLastError() const { return m_lastError; }

    // Progress tracking
    int getProgress() const { return m_progress; }
    QString getCurrentOperation() const { return m_currentOperation; }
    QDateTime getEstimatedCompletion() const;

    // Template management
    bool saveTemplate(const QString &name, const ExportConfig &config);
    bool loadTemplate(const QString &name, ExportConfig &config);
    bool deleteTemplate(const QString &name);
    QStringList getAvailableTemplates() const;

    // Format support
    static QStringList getSupportedFormats();
    static QString getFormatDescription(ExportFormat format);
    static QString getFormatExtension(ExportFormat format);
    static QStringList getFormatMimeTypes(ExportFormat format);

    // Validation
    bool validateConfig(const ExportConfig &config, QString &error) const;
    bool validateOutputPath(const QString &path, QString &error) const;

    // Batch operations
    bool startBatchExport(const QList<ExportConfig> &configs);
    int getBatchProgress() const;
    int getBatchTotal() const;

public slots:
    void exportPackets(const QString &filename, ExportFormat format = ExportFormat::CSV);
    void exportRules(const QString &filename, ExportFormat format = ExportFormat::JSON);
    void exportAlerts(const QString &filename, ExportFormat format = ExportFormat::CSV);
    void exportStatistics(const QString &filename, ExportFormat format = ExportFormat::PDF);
    void exportConfiguration(const QString &filename, ExportFormat format = ExportFormat::JSON);
    void exportAll(const QString &basePath, ExportFormat format = ExportFormat::JSON);

signals:
    void exportStarted(const QString &filename);
    void exportProgress(int percentage);
    void exportStatusChanged(ExportStatus status);
    void exportCompleted(const QString &filename);
    void exportCancelled();
    void exportError(const QString &error);
    void batchExportProgress(int current, int total);
    void batchExportCompleted();

private slots:
    void onExportThreadFinished();
    void onProgressUpdate(int percentage);

private:
    // Export implementations for different formats
    bool exportToCsv(const ExportConfig &config);
    bool exportToJson(const ExportConfig &config);
    bool exportToXml(const ExportConfig &config);
    bool exportToPdf(const ExportConfig &config);
    bool exportToHtml(const ExportConfig &config);
    bool exportToExcel(const ExportConfig &config);
    bool exportToPowerPoint(const ExportConfig &config);
    bool exportToText(const ExportConfig &config);

    // Data extraction methods
    QJsonArray extractPacketData(const ExportConfig &config);
    QJsonArray extractRuleData(const ExportConfig &config);
    QJsonArray extractAlertData(const ExportConfig &config);
    QJsonObject extractStatisticsData(const ExportConfig &config);
    QJsonObject extractConfigurationData(const ExportConfig &config);

    // Utility methods
    QString generateFilename(const ExportConfig &config) const;
    QString formatTimestamp(const QDateTime &timestamp) const;
    QString escapeString(const QString &str, ExportFormat format) const;
    QStringList getFieldsForDataType(ExportDataType dataType) const;

    // Template methods
    QString getTemplateFilePath(const QString &name) const;
    bool loadTemplateFromFile(const QString &filePath, ExportConfig &config);
    bool saveTemplateToFile(const QString &filePath, const ExportConfig &config);

    // PDF generation helpers
    void setupPdfDocument(QPrinter *printer, const ExportConfig &config);
    void generatePdfHeader(QTextDocument *document, const ExportConfig &config);
    void generatePdfFooter(QTextDocument *document, const ExportConfig &config);
    void generatePdfTable(QTextDocument *document, const QJsonArray &data, const QStringList &headers);

    // HTML generation helpers
    QString generateHtmlHeader(const ExportConfig &config);
    QString generateHtmlFooter(const ExportConfig &config);
    QString generateHtmlTable(const QJsonArray &data, const QStringList &headers);
    QString generateHtmlStyles();

    // Excel generation helpers (if available)
    #ifdef XLSX_SUPPORT
    bool createExcelWorkbook(const QString &filename, const ExportConfig &config);
    void addExcelWorksheet(const QString &sheetName, const QJsonArray &data, const QStringList &headers);
    #endif

    // PowerPoint generation helpers (if available)
    #ifdef PPTX_SUPPORT
    bool createPowerPointPresentation(const QString &filename, const ExportConfig &config);
    void addPowerPointSlide(const QString &title, const QJsonArray &data);
    #endif

    // Compression helpers
    bool compressFile(const QString &inputPath, const QString &outputPath);
    bool decompressFile(const QString &inputPath, const QString &outputPath);

    // Data models
    PacketModel *m_packetModel;
    RuleModel *m_ruleModel;
    AlertModel *m_alertModel;

    // Export state
    ExportStatus m_status;
    ExportConfig m_currentConfig;
    QString m_lastError;
    int m_progress;
    QString m_currentOperation;
    QDateTime m_exportStartTime;

    // Batch export state
    QList<ExportConfig> m_batchConfigs;
    int m_batchCurrentIndex;

    // Threading
    QThread *m_exportThread;
    QMutex m_mutex;
    bool m_cancelRequested;

    // Configuration
    QString m_templatesPath;
    int m_maxRecordsPerBatch;
    bool m_useCompression;

    static const int DEFAULT_MAX_RECORDS_PER_BATCH = 10000;
    static const QString TEMPLATES_SUBDIRECTORY;
};

/**
 * @brief Worker class for performing export operations in a separate thread
 */
class ExportWorker : public QObject
{
    Q_OBJECT

public:
    explicit ExportWorker(ExportManager *manager, const ExportManager::ExportConfig &config);
    ~ExportWorker();

public slots:
    void performExport();

signals:
    void progressUpdated(int percentage);
    void operationChanged(const QString &operation);
    void exportCompleted();
    void exportFailed(const QString &error);

private:
    ExportManager *m_manager;
    ExportManager::ExportConfig m_config;
};

Q_DECLARE_METATYPE(ExportManager::ExportConfig)

#endif // EXPORTMANAGER_H
