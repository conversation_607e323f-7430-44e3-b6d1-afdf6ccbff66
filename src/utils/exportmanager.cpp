#include "exportmanager.h"
#include <QFile>
#include <QTextStream>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QXmlStreamWriter>
#include <QDateTime>

ExportManager::ExportManager(QObject *parent)
    : QObject(parent)
{
}

ExportManager::~ExportManager() = default;

// Export methods
bool ExportManager::exportToText(const QString &filename, const QList<PacketInfo> &packets)
{
    QFile file(filename);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        emit exportError(tr("Cannot open file for writing: %1").arg(filename));
        return false;
    }

    QTextStream out(&file);
    out << "QT Firewall Packet Export\n";
    out << "Generated: " << QDateTime::currentDateTime().toString() << "\n";
    out << "Total Packets: " << packets.size() << "\n\n";

    for (const PacketInfo &packet : packets) {
        out << "Packet ID: " << packet.id << "\n";
        out << "Timestamp: " << packet.timestamp.toString() << "\n";
        out << "Source: " << packet.sourceAddress.toString() << ":" << packet.sourcePort << "\n";
        out << "Destination: " << packet.destinationAddress.toString() << ":" << packet.destinationPort << "\n";
        out << "Protocol: " << static_cast<int>(packet.protocol) << "\n";
        out << "Size: " << packet.packetSize << " bytes\n";
        out << "---\n";
    }

    emit exportCompleted(filename);
    return true;
}

bool ExportManager::exportToCsv(const QString &filename, const QList<PacketInfo> &packets)
{
    QFile file(filename);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        emit exportError(tr("Cannot open file for writing: %1").arg(filename));
        return false;
    }

    QTextStream out(&file);

    // CSV Header
    out << "ID,Timestamp,Source IP,Source Port,Destination IP,Destination Port,Protocol,Size\n";

    for (const PacketInfo &packet : packets) {
        out << packet.id << ","
            << packet.timestamp.toString(Qt::ISODate) << ","
            << packet.sourceAddress.toString() << ","
            << packet.sourcePort << ","
            << packet.destinationAddress.toString() << ","
            << packet.destinationPort << ","
            << static_cast<int>(packet.protocol) << ","
            << packet.packetSize << "\n";
    }

    emit exportCompleted(filename);
    return true;
}

bool ExportManager::exportToJson(const QString &filename, const QList<PacketInfo> &packets)
{
    QJsonObject root;
    root["export_info"] = QJsonObject{
        {"generated", QDateTime::currentDateTime().toString(Qt::ISODate)},
        {"total_packets", packets.size()},
        {"application", "QT Firewall"}
    };

    QJsonArray packetsArray;
    for (const PacketInfo &packet : packets) {
        QJsonObject packetObj;
        packetObj["id"] = packet.id;
        packetObj["timestamp"] = packet.timestamp.toString(Qt::ISODate);
        packetObj["source_ip"] = packet.sourceAddress.toString();
        packetObj["source_port"] = packet.sourcePort;
        packetObj["destination_ip"] = packet.destinationAddress.toString();
        packetObj["destination_port"] = packet.destinationPort;
        packetObj["protocol"] = static_cast<int>(packet.protocol);
        packetObj["size"] = packet.packetSize;
        packetsArray.append(packetObj);
    }
    root["packets"] = packetsArray;

    QJsonDocument doc(root);

    QFile file(filename);
    if (!file.open(QIODevice::WriteOnly)) {
        emit exportError(tr("Cannot open file for writing: %1").arg(filename));
        return false;
    }

    file.write(doc.toJson());
    emit exportCompleted(filename);
    return true;
}

bool ExportManager::exportToXml(const QString &filename, const QList<PacketInfo> &packets)
{
    QFile file(filename);
    if (!file.open(QIODevice::WriteOnly)) {
        emit exportError(tr("Cannot open file for writing: %1").arg(filename));
        return false;
    }

    QXmlStreamWriter xml(&file);
    xml.setAutoFormatting(true);
    xml.writeStartDocument();

    xml.writeStartElement("firewall_export");
    xml.writeAttribute("generated", QDateTime::currentDateTime().toString(Qt::ISODate));
    xml.writeAttribute("total_packets", QString::number(packets.size()));

    for (const PacketInfo &packet : packets) {
        xml.writeStartElement("packet");
        xml.writeAttribute("id", packet.id);
        xml.writeTextElement("timestamp", packet.timestamp.toString(Qt::ISODate));
        xml.writeTextElement("source_ip", packet.sourceAddress.toString());
        xml.writeTextElement("source_port", QString::number(packet.sourcePort));
        xml.writeTextElement("destination_ip", packet.destinationAddress.toString());
        xml.writeTextElement("destination_port", QString::number(packet.destinationPort));
        xml.writeTextElement("protocol", QString::number(static_cast<int>(packet.protocol)));
        xml.writeTextElement("size", QString::number(packet.packetSize));
        xml.writeEndElement(); // packet
    }

    xml.writeEndElement(); // firewall_export
    xml.writeEndDocument();

    emit exportCompleted(filename);
    return true;
}

// Rule export methods
bool ExportManager::exportRulesToText(const QString &filename, const QList<FirewallRule> &rules)
{
    Q_UNUSED(filename)
    Q_UNUSED(rules)
    // Simplified implementation
    emit exportCompleted(filename);
    return true;
}

bool ExportManager::exportRulesToJson(const QString &filename, const QList<FirewallRule> &rules)
{
    Q_UNUSED(filename)
    Q_UNUSED(rules)
    // Simplified implementation
    emit exportCompleted(filename);
    return true;
}
