#include "configmanager.h"
#include <QStandardPaths>
#include <QDir>
#include <QMutexLocker>
#include <QCoreApplication>

ConfigManager* ConfigManager::s_instance = nullptr;

ConfigManager& ConfigManager::instance()
{
    if (!s_instance) {
        s_instance = new ConfigManager();
    }
    return *s_instance;
}

ConfigManager::ConfigManager(QObject *parent)
    : QObject(parent)
    , m_settings(nullptr)
{
}

ConfigManager::~ConfigManager()
{
    if (m_settings) {
        m_settings->sync();
        delete m_settings;
    }
}

bool ConfigManager::initialize(const QString &configFile)
{
    QMutexLocker locker(&m_mutex);

    if (m_settings) {
        delete m_settings;
    }

    if (configFile.isEmpty()) {
        // Use default application settings location
        m_settings = new QSettings(QSettings::IniFormat, QSettings::UserScope,
                                   QCoreApplication::organizationName(),
                                   QCoreApplication::applicationName());
    } else {
        // Use custom config file
        m_settings = new QSettings(configFile, QSettings::IniFormat);
    }

    if (!m_settings) {
        return false;
    }

    // Set default values if they don't exist
    setDefaultValues();

    return true;
}

QVariant ConfigManager::getValue(const QString &key, const QVariant &defaultValue) const
{
    QMutexLocker locker(&m_mutex);
    if (!m_settings) {
        return defaultValue;
    }
    return m_settings->value(key, defaultValue);
}

void ConfigManager::setValue(const QString &key, const QVariant &value)
{
    QMutexLocker locker(&m_mutex);
    if (m_settings) {
        m_settings->setValue(key, value);
    }
}

void ConfigManager::sync()
{
    QMutexLocker locker(&m_mutex);
    if (m_settings) {
        m_settings->sync();
    }
}

void ConfigManager::reset()
{
    QMutexLocker locker(&m_mutex);
    if (m_settings) {
        m_settings->clear();
        setDefaultValues();
        m_settings->sync();
    }
}

void ConfigManager::setDefaultValues()
{
    if (!m_settings) return;

    // Application defaults
    if (!m_settings->contains("app/first_run")) {
        m_settings->setValue("app/first_run", true);
    }
    if (!m_settings->contains("app/version")) {
        m_settings->setValue("app/version", QCoreApplication::applicationVersion());
    }

    // UI defaults
    if (!m_settings->contains("ui/theme")) {
        m_settings->setValue("ui/theme", "dark");
    }
    if (!m_settings->contains("ui/window_geometry")) {
        m_settings->setValue("ui/window_geometry", QByteArray());
    }
    if (!m_settings->contains("ui/window_state")) {
        m_settings->setValue("ui/window_state", QByteArray());
    }

    // Network defaults
    if (!m_settings->contains("network/interfaces")) {
        m_settings->setValue("network/interfaces", QStringList());
    }
    if (!m_settings->contains("network/packet_buffer_size")) {
        m_settings->setValue("network/packet_buffer_size", 10000);
    }
    if (!m_settings->contains("network/capture_timeout")) {
        m_settings->setValue("network/capture_timeout", 1000);
    }

    // Firewall defaults
    if (!m_settings->contains("firewall/default_policy")) {
        m_settings->setValue("firewall/default_policy", "allow");
    }
    if (!m_settings->contains("firewall/log_blocked")) {
        m_settings->setValue("firewall/log_blocked", true);
    }
    if (!m_settings->contains("firewall/log_allowed")) {
        m_settings->setValue("firewall/log_allowed", false);
    }

    // Logging defaults
    if (!m_settings->contains("logging/level")) {
        m_settings->setValue("logging/level", "info");
    }
    if (!m_settings->contains("logging/file_enabled")) {
        m_settings->setValue("logging/file_enabled", true);
    }
    if (!m_settings->contains("logging/console_enabled")) {
        m_settings->setValue("logging/console_enabled", true);
    }
}
