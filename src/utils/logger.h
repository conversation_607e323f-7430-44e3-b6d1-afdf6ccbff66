#ifndef LOGGER_H
#define LOGGER_H

#include <QObject>
#include <QFile>
#include <QTextStream>
#include <QMutex>
#include <QDateTime>
#include <QDebug>

/**
 * @brief Simple logging utility for the QT Firewall application
 *
 * This class provides centralized logging functionality with different
 * log levels, file output, and thread-safe operation.
 */
class Logger : public QObject
{
    Q_OBJECT

public:
    enum LogLevel {
        DEBUG = 0,
        INFO,
        WARNING,
        ERROR,
        CRITICAL
    };

    static void initialize(LogLevel level = INFO);
    static void cleanup();
    static Logger& instance();

    void setLogLevel(LogLevel level);
    void setLogFile(const QString &filename);
    void enableConsoleOutput(bool enabled);

    void log(LogLevel level, const QString &message, const QString &category = QString());

    // Convenience methods
    void debug(const QString &message, const QString &category = QString());
    void info(const QString &message, const QString &category = QString());
    void warning(const QString &message, const QString &category = QString());
    void error(const QString &message, const QString &category = QString());
    void critical(const QString &message, const QString &category = QString());

private:
    explicit Logger(QObject *parent = nullptr);
    ~Logger();

    QString levelToString(LogLevel level) const;
    QString formatMessage(LogLevel level, const QString &message, const QString &category) const;

    static Logger *s_instance;
    LogLevel m_logLevel;
    QFile *m_logFile;
    QTextStream *m_logStream;
    bool m_consoleOutput;
    QMutex m_mutex;
};

// Global logging macros
#define LOG_DEBUG(msg) Logger::instance().debug(msg, Q_FUNC_INFO)
#define LOG_INFO(msg) Logger::instance().info(msg, Q_FUNC_INFO)
#define LOG_WARNING(msg) Logger::instance().warning(msg, Q_FUNC_INFO)
#define LOG_ERROR(msg) Logger::instance().error(msg, Q_FUNC_INFO)
#define LOG_CRITICAL(msg) Logger::instance().critical(msg, Q_FUNC_INFO)

#endif // LOGGER_H
