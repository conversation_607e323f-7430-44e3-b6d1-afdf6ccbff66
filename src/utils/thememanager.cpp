#include "thememanager.h"
#include <QApplication>
#include <QFile>
#include <QTextStream>
#include <QDir>
#include <QStandardPaths>

ThemeManager* ThemeManager::s_instance = nullptr;

ThemeManager& ThemeManager::instance()
{
    if (!s_instance) {
        s_instance = new ThemeManager();
    }
    return *s_instance;
}

ThemeManager::ThemeManager(QObject *parent)
    : QObject(parent)
    , m_currentTheme("dark")
{
}

ThemeManager::~ThemeManager()
{
}

void ThemeManager::initialize()
{
    // Initialize with default theme
    applyTheme(m_currentTheme);
}

void ThemeManager::applyTheme(const QString &themeName)
{
    if (themeName == m_currentTheme) {
        return; // Already applied
    }

    QString stylesheet = loadThemeStylesheet(themeName);
    if (!stylesheet.isEmpty()) {
        QApplication::instance()->setStyleSheet(stylesheet);
        m_currentTheme = themeName;
        emit themeChanged(themeName);
    }
}

QStringList ThemeManager::getAvailableThemes() const
{
    return QStringList() << "dark" << "light";
}

QString ThemeManager::getCurrentTheme() const
{
    return m_currentTheme;
}

QString ThemeManager::loadThemeStylesheet(const QString &themeName)
{
    QString resourcePath = QString(":/themes/%1.qss").arg(themeName);
    QFile file(resourcePath);

    if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QTextStream stream(&file);
        return stream.readAll();
    }

    // Fallback to file system if resource not found
    QString filePath = QString("resources/themes/%1.qss").arg(themeName);
    QFile fallbackFile(filePath);
    if (fallbackFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QTextStream stream(&fallbackFile);
        return stream.readAll();
    }

    return QString(); // Return empty string if theme not found
}
