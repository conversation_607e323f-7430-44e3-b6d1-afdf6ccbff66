#ifndef ANIMATIONHELPER_H
#define ANIMATIONHELPER_H

#include <QObject>
#include <QWidget>
#include <QPropertyAnimation>
#include <QParallelAnimationGroup>
#include <QSequentialAnimationGroup>
#include <QGraphicsOpacityEffect>
#include <QGraphicsDropShadowEffect>
#include <QGraphicsBlurEffect>
#include <QEasingCurve>
#include <QColor>
#include <QRect>
#include <QPoint>
#include <QSize>

/**
 * @brief Animation utility functions and helpers for consistent UI animations
 *
 * This class provides a comprehensive set of animation utilities for creating
 * smooth, consistent animations throughout the application. It includes:
 * - Pre-configured animation presets for common UI patterns
 * - Easing curve utilities and recommendations
 * - Animation chaining and sequencing helpers
 * - Performance-optimized animation settings
 * - Cross-platform animation compatibility
 * - Memory management for animation objects
 */
class AnimationHelper : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Common animation types with predefined settings
     */
    enum AnimationType {
        FADE_IN = 0,        ///< Fade in animation
        FADE_OUT,           ///< Fade out animation
        SLIDE_IN_LEFT,      ///< Slide in from left
        SLIDE_IN_RIGHT,     ///< Slide in from right
        SLIDE_IN_TOP,       ///< Slide in from top
        SLIDE_IN_BOTTOM,    ///< Slide in from bottom
        SLIDE_OUT_LEFT,     ///< Slide out to left
        SLIDE_OUT_RIGHT,    ///< Slide out to right
        SLIDE_OUT_TOP,      ///< Slide out to top
        SLIDE_OUT_BOTTOM,   ///< Slide out to bottom
        SCALE_IN,           ///< Scale in animation
        SCALE_OUT,          ///< Scale out animation
        BOUNCE_IN,          ///< Bounce in animation
        BOUNCE_OUT,         ///< Bounce out animation
        ROTATE_IN,          ///< Rotate in animation
        ROTATE_OUT,         ///< Rotate out animation
        PULSE,              ///< Pulse animation
        SHAKE,              ///< Shake animation
        GLOW,               ///< Glow effect animation
        BLUR_IN,            ///< Blur in animation
        BLUR_OUT            ///< Blur out animation
    };

    /**
     * @brief Animation speed presets
     */
    enum AnimationSpeed {
        VERY_SLOW = 0,      ///< 1000ms
        SLOW,               ///< 500ms
        NORMAL,             ///< 300ms
        FAST,               ///< 200ms
        VERY_FAST           ///< 100ms
    };

    /**
     * @brief Animation easing presets
     */
    enum EasingPreset {
        SMOOTH = 0,         ///< OutQuad - smooth deceleration
        BOUNCE,             ///< OutBounce - bouncy effect
        ELASTIC,            ///< OutElastic - elastic effect
        BACK,               ///< OutBack - overshoot effect
        CUBIC,              ///< OutCubic - cubic deceleration
        SINE,               ///< InOutSine - smooth acceleration/deceleration
        EXPO,               ///< OutExpo - exponential deceleration
        CIRC                ///< OutCirc - circular deceleration
    };

    explicit AnimationHelper(QObject *parent = nullptr);
    ~AnimationHelper();

    // Static convenience methods for common animations
    static QPropertyAnimation* createFadeAnimation(QWidget *widget, qreal startOpacity, qreal endOpacity,
                                                  int duration = 300, QEasingCurve::Type easing = QEasingCurve::OutQuad);

    static QPropertyAnimation* createSlideAnimation(QWidget *widget, const QPoint &startPos, const QPoint &endPos,
                                                   int duration = 300, QEasingCurve::Type easing = QEasingCurve::OutQuad);

    static QPropertyAnimation* createScaleAnimation(QWidget *widget, qreal startScale, qreal endScale,
                                                   int duration = 300, QEasingCurve::Type easing = QEasingCurve::OutQuad);

    static QPropertyAnimation* createRotationAnimation(QWidget *widget, qreal startAngle, qreal endAngle,
                                                      int duration = 300, QEasingCurve::Type easing = QEasingCurve::OutQuad);

    static QPropertyAnimation* createColorAnimation(QObject *target, const QByteArray &property,
                                                   const QColor &startColor, const QColor &endColor,
                                                   int duration = 300, QEasingCurve::Type easing = QEasingCurve::OutQuad);

    static QPropertyAnimation* createSizeAnimation(QWidget *widget, const QSize &startSize, const QSize &endSize,
                                                  int duration = 300, QEasingCurve::Type easing = QEasingCurve::OutQuad);

    static QPropertyAnimation* createGeometryAnimation(QWidget *widget, const QRect &startGeometry, const QRect &endGeometry,
                                                      int duration = 300, QEasingCurve::Type easing = QEasingCurve::OutQuad);

    // Preset animations
    static QAbstractAnimation* createPresetAnimation(QWidget *widget, AnimationType type,
                                                    AnimationSpeed speed = NORMAL, EasingPreset easing = SMOOTH);

    // Animation groups
    static QParallelAnimationGroup* createParallelGroup(const QList<QAbstractAnimation*> &animations);
    static QSequentialAnimationGroup* createSequentialGroup(const QList<QAbstractAnimation*> &animations);

    // Effect animations
    static QPropertyAnimation* createGlowAnimation(QWidget *widget, const QColor &glowColor, qreal maxRadius = 20.0,
                                                  int duration = 1000, bool loop = true);

    static QPropertyAnimation* createBlurAnimation(QWidget *widget, qreal startRadius, qreal endRadius,
                                                  int duration = 300, QEasingCurve::Type easing = QEasingCurve::OutQuad);

    static QSequentialAnimationGroup* createPulseAnimation(QWidget *widget, qreal minScale = 0.95, qreal maxScale = 1.05,
                                                          int duration = 1000, bool loop = true);

    static QSequentialAnimationGroup* createShakeAnimation(QWidget *widget, int intensity = 5, int duration = 500);

    static QSequentialAnimationGroup* createBounceAnimation(QWidget *widget, int bounceHeight = 20, int duration = 800);

    // Utility methods
    static int getDurationForSpeed(AnimationSpeed speed);
    static QEasingCurve::Type getEasingForPreset(EasingPreset preset);
    static QEasingCurve createCustomEasing(qreal amplitude = 1.0, qreal period = 0.3);

    // Animation management
    static void startAnimation(QAbstractAnimation *animation, bool deleteOnFinish = true);
    static void stopAnimation(QAbstractAnimation *animation);
    static void pauseAnimation(QAbstractAnimation *animation);
    static void resumeAnimation(QAbstractAnimation *animation);

    // Performance optimization
    static void setAnimationsEnabled(bool enabled);
    static bool areAnimationsEnabled();
    static void setReducedMotion(bool reduced);
    static bool isReducedMotion();

    // Widget state helpers
    static void saveWidgetState(QWidget *widget);
    static void restoreWidgetState(QWidget *widget);
    static void prepareWidgetForAnimation(QWidget *widget);
    static void cleanupWidgetAfterAnimation(QWidget *widget);

    // Animation chaining
    static void chainAnimations(QAbstractAnimation *first, QAbstractAnimation *second);
    static void delayAnimation(QAbstractAnimation *animation, int delayMs);

    // Visual effects setup
    static QGraphicsOpacityEffect* setupOpacityEffect(QWidget *widget);
    static QGraphicsDropShadowEffect* setupShadowEffect(QWidget *widget, const QColor &color = QColor(0, 0, 0, 100));
    static QGraphicsBlurEffect* setupBlurEffect(QWidget *widget);

    // Animation curves
    static QEasingCurve createBounceEasing(qreal amplitude = 1.0);
    static QEasingCurve createElasticEasing(qreal amplitude = 1.0, qreal period = 0.3);
    static QEasingCurve createBackEasing(qreal overshoot = 1.70158);

public slots:
    void onAnimationFinished();
    void onAnimationStateChanged(QAbstractAnimation::State newState, QAbstractAnimation::State oldState);

signals:
    void animationStarted(QAbstractAnimation *animation);
    void animationFinished(QAbstractAnimation *animation);
    void animationPaused(QAbstractAnimation *animation);
    void animationResumed(QAbstractAnimation *animation);

private:
    static void connectAnimationSignals(QAbstractAnimation *animation);
    static void setupAnimationDefaults(QPropertyAnimation *animation);

    // Global settings
    static bool s_animationsEnabled;
    static bool s_reducedMotion;
    static QMap<QWidget*, QVariantMap> s_widgetStates;
    static QList<QAbstractAnimation*> s_activeAnimations;

    // Duration constants
    static const int VERY_SLOW_DURATION = 1000;
    static const int SLOW_DURATION = 500;
    static const int NORMAL_DURATION = 300;
    static const int FAST_DURATION = 200;
    static const int VERY_FAST_DURATION = 100;
};

#endif // ANIMATIONHELPER_H
