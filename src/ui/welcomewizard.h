#ifndef WELCOMEWIZARD_H
#define WELCOMEWIZARD_H

#include <QWizard>
#include <QWizardPage>

/**
 * @brief Welcome wizard for first-time application setup
 */
class WelcomeWizard : public QWizard
{
    Q_OBJECT

public:
    explicit WelcomeWizard(QWidget *parent = nullptr);
    ~WelcomeWizard();

private:
    void setupPages();
};

/**
 * @brief Welcome page with animated introduction
 */
class WelcomePage : public QWizardPage
{
    Q_OBJECT

public:
    explicit WelcomePage(QWidget *parent = nullptr);

private:
    void setupUI();
};

/**
 * @brief Network interface selection page
 */
class InterfaceSelectionPage : public QWizardPage
{
    Q_OBJECT

public:
    explicit InterfaceSelectionPage(QWidget *parent = nullptr);

private:
    void setupUI();
    void loadNetworkInterfaces();
};

/**
 * @brief Rule template selection page
 */
class RuleTemplatePage : public QWizardPage
{
    Q_OBJECT

public:
    explicit RuleTemplatePage(QWidget *parent = nullptr);

private:
    void setupUI();
};

/**
 * @brief Configuration completion page
 */
class CompletionPage : public QWizardPage
{
    Q_OBJECT

public:
    explicit CompletionPage(QWidget *parent = nullptr);

private:
    void setupUI();
};

#endif // WELCOMEWIZARD_H
