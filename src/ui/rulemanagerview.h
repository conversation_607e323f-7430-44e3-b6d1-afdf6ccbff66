#ifndef RULEMANAGERVIEW_H
#define RULEMANAGERVIEW_H

#include <QWidget>

class RuleManageView : public QWidget
{
    Q_OBJECT

public:
    explicit RuleManageView(QWidget *parent = nullptr);
    ~RuleManageView();

private:
    void setupUI();
};

#endif // RULEMANAGERVIEW_H

namespace Ui {
class RuleManagerView;
}

class RuleManagerView : public QWidget
{
    Q_OBJECT

public:
    explicit RuleManagerView(QWidget *parent = nullptr);
    ~RuleManagerView();

private:
    Ui::RuleManagerView *ui;
};

#endif // RULEMANAGERVIEW_H
