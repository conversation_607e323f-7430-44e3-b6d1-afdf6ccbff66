#ifndef ALERTVIEW_H
#define ALERTVIEW_H

#include <QWidget>

class AlertView : public QWidget
{
    Q_OBJECT

public:
    explicit AlertView(QWidget *parent = nullptr);
    ~AlertView();

private:
    void setupUI();
};

#endif // ALERTVIEW_H

namespace Ui {
class AlertView;
}

class AlertView : public QWidget
{
    Q_OBJECT

public:
    explicit AlertView(QWidget *parent = nullptr);
    ~AlertView();

private:
    Ui::AlertView *ui;
};

#endif // ALERTVIEW_H
