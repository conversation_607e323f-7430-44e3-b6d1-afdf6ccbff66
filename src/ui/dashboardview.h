#ifndef DASHBOARDVIEW_H
#define DASHBOARDVIEW_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QGroupBox>
#include <QProgressBar>
#include <QTimer>
#include <QDateTime> // Added QDateTime include

class NetworkVisualizationWidget;
class StatisticsWidget;
class PacketFlowWidget;
class ProtocolFilterWidget;
class PacketModel;
class AnimatedButton;
class QPropertyAnimation;
class QParallelAnimationGroup;
class QSequentialAnimationGroup; // Added missing forward declaration for QSequentialAnimationGroup

namespace Ui {
class DashboardView;
}

/**
 * @brief Main dashboard view showing network overview and real-time statistics
 */
class DashboardView : public QWidget
{
    Q_OBJECT

public:
    explicit DashboardView(QWidget *parent = nullptr);
    ~DashboardView();

public slots:
    void startMonitoring();
    void stopMonitoring();
    void clearData();
    void exportData();
    void refreshView();

private slots:
    void updateStatistics();
    void onProtocolFilterChanged();
    void onPacketReceived(const PacketInfo &packet);
    void onNetworkNodeClicked(const QString &nodeId);
    void onAnimationFinished();
    void toggleFullscreen();

signals:
    void monitoringStarted();
    void monitoringStopped();
    void dataExported(const QString &filename);
    void nodeSelected(const QString &nodeId);

private:
    void setupUI();
    void setupStatisticsPanel();
    void setupVisualizationPanel();
    void setupControlPanel();
    void setupAnimations();
    void setupConnections();
    void startWelcomeAnimation();
    void updateNetworkVisualization();
    void updatePacketFlow();
    void animateStatisticsUpdate();

    // Helper methods for UI creation
    QLabel* createAnimatedLabel(const QString &text, const QString &color);
    QProgressBar* createStyledProgressBar(const QString &color);
    QWidget* createVisualizationControlPanel();

    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_topLayout;
    QHBoxLayout *m_bottomLayout;

    // Statistics widgets
    QGroupBox *m_statsGroup;
    QLabel *m_packetsPerSecLabel;
    QLabel *m_totalPacketsLabel;
    QLabel *m_blockedPacketsLabel;
    QLabel *m_allowedPacketsLabel;
    QProgressBar *m_cpuUsageBar;
    QProgressBar *m_memoryUsageBar;
    QLabel *m_networkStatusLabel;

    // Visualization widgets
    NetworkVisualizationWidget *m_networkViz;
    PacketFlowWidget *m_packetFlow;
    ProtocolFilterWidget *m_protocolFilter;
    StatisticsWidget *m_statisticsWidget;

    // Data models
    PacketModel *m_packetModel;

    // Animation system
    QParallelAnimationGroup *m_animationGroup;
    QPropertyAnimation *m_fadeAnimation;
    QPropertyAnimation *m_slideAnimation;

    // Control buttons
    AnimatedButton *m_startButton;
    AnimatedButton *m_stopButton;
    AnimatedButton *m_clearButton;
    AnimatedButton *m_exportButton;

    // Update timer
    QTimer *m_updateTimer;

    // State tracking
    bool m_isMonitoring;
    quint64 m_totalPacketCount;
    quint64 m_blockedPacketCount;
    quint64 m_allowedPacketCount;
    int m_packetsPerSecond;
    QDateTime m_lastUpdateTime;

    Ui::DashboardView *ui;
};

#endif // DASHBOARDVIEW_H
