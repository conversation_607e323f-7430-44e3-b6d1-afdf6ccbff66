#ifndef DASHBOARDVIEW_H
#define DASHBOARDVIEW_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QGroupBox>
#include <QProgressBar>
#include <QTimer>

class NetworkVisualizationWidget;
class StatisticsWidget;
class PacketFlowWidget;
class ProtocolFilterWidget;

/**
 * @brief Main dashboard view showing network overview and real-time statistics
 */
class DashboardView : public QWidget
{
    Q_OBJECT

public:
    explicit DashboardView(QWidget *parent = nullptr);
    ~DashboardView();

private slots:
    void updateStatistics();
    void onProtocolFilterChanged();

private:
    void setupUI();
    void setupStatisticsPanel();
    void setupVisualizationPanel();
    void setupControlPanel();

    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_topLayout;
    QHBoxLayout *m_bottomLayout;

    // Statistics widgets
    QGroupBox *m_statsGroup;
    QLabel *m_packetsPerSecLabel;
    QLabel *m_totalPacketsLabel;
    QLabel *m_blockedPacketsLabel;
    QLabel *m_allowedPacketsLabel;
    QProgressBar *m_cpuUsageBar;
    QProgressBar *m_memoryUsageBar;

    // Visualization widgets
    NetworkVisualizationWidget *m_networkViz;
    PacketFlowWidget *m_packetFlow;
    ProtocolFilterWidget *m_protocolFilter;

    // Update timer
    QTimer *m_updateTimer;
};

#endif // DASHBOARDVIEW_H

namespace Ui {
class DashboardView;
}

class DashboardView : public QWidget
{
    Q_OBJECT

public:
    explicit DashboardView(QWidget *parent = nullptr);
    ~DashboardView();

private:
    Ui::DashboardView *ui;
};

#endif // DASHBOARDVIEW_H
