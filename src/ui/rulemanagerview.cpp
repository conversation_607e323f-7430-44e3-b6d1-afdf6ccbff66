#include "rulemanagerview.h"
#include <QVBoxLayout>
#include <QLabel>

RuleManageView::RuleManageView(QWidget *parent) : QWidget(parent)
{
    setupUI();
}

RuleManageView::~RuleManageView() = default;

void RuleManageView::setupUI()
{
    QVBoxLayout *layout = new QVBoxLayout(this);
    layout->addWidget(new QLabel("Rule Management View - Coming Soon"));
}

RuleManagerView::RuleManagerView(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::RuleManagerView)
{
    ui->setupUi(this);
    qDebug() << "RuleManagerView initialized.";
}

RuleManagerView::~RuleManagerView()
{
    delete ui;
    qDebug() << "RuleManagerView destroyed.";
}
