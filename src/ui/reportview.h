#ifndef REPORTVIEW_H
#define REPORTVIEW_H
#include <QWidget>
class ReportView : public QWidget
{
    Q_OBJECT
public:
    explicit ReportView(QWidget *parent = nullptr);
    ~ReportView();
private:
    void setupUI();
};
#endif // REPORTVIEW_H

namespace Ui {
class ReportView;
}

class ReportView : public QWidget
{
    Q_OBJECT

public:
    explicit ReportView(QWidget *parent = nullptr);
    ~ReportView();

private:
    Ui::ReportView *ui;
};

#endif // REPORTVIEW_H
