#include "reportview.h"
#include <QVBoxLayout>
#include <QLabel>
ReportView::ReportView(QWidget *parent) : QWidget(parent) { setupUI(); }
ReportView::~ReportView() = default;
void ReportView::setupUI() {
    QVBoxLayout *layout = new QVBoxLayout(this);
    layout->addWidget(new QLabel("Report Generation View - Coming Soon"));
}

ReportView::ReportView(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::ReportView)
{
    ui->setupUi(this);
    qDebug() << "ReportView initialized.";
}

ReportView::~ReportView()
{
    delete ui;
    qDebug() << "ReportView destroyed.";
}
