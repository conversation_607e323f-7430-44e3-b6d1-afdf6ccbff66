#include "settingsview.h"
#include <QVBoxLayout>
#include <QLabel>
SettingsView::SettingsView(QWidget *parent) : QWidget(parent) { setupUI(); }
SettingsView::~SettingsView() = default;
void SettingsView::setupUI() {
    QVBoxLayout *layout = new QVBoxLayout(this);
    layout->addWidget(new QLabel("Application Settings - Coming Soon"));
}

SettingsView::SettingsView(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::SettingsView)
{
    ui->setupUi(this);
    qDebug() << "SettingsView initialized.";
}

SettingsView::~SettingsView()
{
    delete ui;
    qDebug() << "SettingsView destroyed.";
}
