#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QStackedWidget>
#include <QMenuBar>
#include <QStatusBar>
#include <QToolBar>

class DashboardView;
class RuleManagerView;
class AlertView;
class ReportView;
class SettingsView;

/**
 * @brief Main application window that hosts all views and provides navigation
 */
class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void showDashboard();
    void showRuleManager();
    void showAlerts();
    void showReports();
    void showSettings();
    void showAbout();

private:
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupStatusBar();
    void setupConnections();

    QStackedWidget *m_centralStack;
    DashboardView *m_dashboardView;
    RuleManagerView *m_ruleView;
    AlertView *m_alertView;
    ReportView *m_reportView;
    SettingsView *m_settingsView;

    QToolBar *m_mainToolBar;
};

#endif // MAINWINDOW_H
