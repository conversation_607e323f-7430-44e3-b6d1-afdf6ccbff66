#include "dashboardview.h"
#include "widgets/networkvisualizationwidget.h"
#include "../utils/logger.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QGroupBox>
#include <QProgressBar>
#include <QTimer>
#include <QPushButton>
#include <QSplitter>

DashboardView::DashboardView(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_topLayout(nullptr)
    , m_bottomLayout(nullptr)
    , m_statsGroup(nullptr)
    , m_packetsPerSecLabel(nullptr)
    , m_totalPacketsLabel(nullptr)
    , m_blockedPacketsLabel(nullptr)
    , m_allowedPacketsLabel(nullptr)
    , m_cpuUsageBar(nullptr)
    , m_memoryUsageBar(nullptr)
    , m_networkViz(nullptr)
    , m_packetFlow(nullptr)
    , m_protocolFilter(nullptr)
    , m_updateTimer(nullptr)
{
    setupUI();

    // Setup update timer
    m_updateTimer = new QTimer(this);
    connect(m_updateTimer, &QTimer::timeout, this, &DashboardView::updateStatistics);
    m_updateTimer->start(1000); // Update every second

    LOG_INFO("DashboardView initialized");
}

DashboardView::~DashboardView()
{
    if (m_updateTimer) {
        m_updateTimer->stop();
    }
    LOG_INFO("DashboardView destroyed");
}

void DashboardView::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    m_mainLayout->setSpacing(10);

    setupStatisticsPanel();
    setupVisualizationPanel();
    setupControlPanel();
}

void DashboardView::setupStatisticsPanel()
{
    m_statsGroup = new QGroupBox("Network Statistics", this);
    QGridLayout *statsLayout = new QGridLayout(m_statsGroup);

    // Packet statistics
    m_packetsPerSecLabel = new QLabel("Packets/sec: 0");
    m_totalPacketsLabel = new QLabel("Total Packets: 0");
    m_blockedPacketsLabel = new QLabel("Blocked: 0");
    m_allowedPacketsLabel = new QLabel("Allowed: 0");

    // System statistics
    QLabel *cpuLabel = new QLabel("CPU Usage:");
    m_cpuUsageBar = new QProgressBar();
    m_cpuUsageBar->setRange(0, 100);
    m_cpuUsageBar->setValue(0);

    QLabel *memoryLabel = new QLabel("Memory Usage:");
    m_memoryUsageBar = new QProgressBar();
    m_memoryUsageBar->setRange(0, 100);
    m_memoryUsageBar->setValue(0);

    // Layout statistics
    statsLayout->addWidget(m_packetsPerSecLabel, 0, 0);
    statsLayout->addWidget(m_totalPacketsLabel, 0, 1);
    statsLayout->addWidget(m_blockedPacketsLabel, 0, 2);
    statsLayout->addWidget(m_allowedPacketsLabel, 0, 3);

    statsLayout->addWidget(cpuLabel, 1, 0);
    statsLayout->addWidget(m_cpuUsageBar, 1, 1);
    statsLayout->addWidget(memoryLabel, 1, 2);
    statsLayout->addWidget(m_memoryUsageBar, 1, 3);

    m_mainLayout->addWidget(m_statsGroup);
}

void DashboardView::setupVisualizationPanel()
{
    QSplitter *splitter = new QSplitter(Qt::Horizontal, this);

    // Network visualization
    m_networkViz = new NetworkVisualizationWidget(this);
    splitter->addWidget(m_networkViz);

    // Control panel (placeholder for now)
    QWidget *controlPanel = new QWidget(this);
    controlPanel->setMaximumWidth(250);
    controlPanel->setMinimumWidth(200);

    QVBoxLayout *controlLayout = new QVBoxLayout(controlPanel);
    controlLayout->addWidget(new QLabel("Protocol Filters:"));

    // Add some placeholder controls
    QPushButton *ipBtn = new QPushButton("IP");
    QPushButton *tcpBtn = new QPushButton("TCP");
    QPushButton *udpBtn = new QPushButton("UDP");
    QPushButton *arpBtn = new QPushButton("ARP");

    ipBtn->setCheckable(true);
    tcpBtn->setCheckable(true);
    udpBtn->setCheckable(true);
    arpBtn->setCheckable(true);

    ipBtn->setChecked(true);
    tcpBtn->setChecked(true);
    udpBtn->setChecked(true);
    arpBtn->setChecked(true);

    controlLayout->addWidget(ipBtn);
    controlLayout->addWidget(tcpBtn);
    controlLayout->addWidget(udpBtn);
    controlLayout->addWidget(arpBtn);
    controlLayout->addStretch();

    splitter->addWidget(controlPanel);
    splitter->setSizes({800, 200});

    m_mainLayout->addWidget(splitter, 1);
}

void DashboardView::setupControlPanel()
{
    QHBoxLayout *controlLayout = new QHBoxLayout();

    QPushButton *startBtn = new QPushButton("Start Monitoring");
    QPushButton *stopBtn = new QPushButton("Stop Monitoring");
    QPushButton *clearBtn = new QPushButton("Clear Data");
    QPushButton *exportBtn = new QPushButton("Export Data");

    controlLayout->addWidget(startBtn);
    controlLayout->addWidget(stopBtn);
    controlLayout->addWidget(clearBtn);
    controlLayout->addWidget(exportBtn);
    controlLayout->addStretch();

    m_mainLayout->addLayout(controlLayout);
}

void DashboardView::updateStatistics()
{
    // TODO: Get real statistics from packet capture engine
    static int packetCount = 0;
    static int packetsPerSec = 0;
    static int blockedCount = 0;
    static int allowedCount = 0;

    // Simulate some activity
    packetCount += QRandomGenerator::global()->bounded(0, 10);
    packetsPerSec = QRandomGenerator::global()->bounded(0, 100);
    blockedCount += QRandomGenerator::global()->bounded(0, 2);
    allowedCount = packetCount - blockedCount;

    m_packetsPerSecLabel->setText(QString("Packets/sec: %1").arg(packetsPerSec));
    m_totalPacketsLabel->setText(QString("Total Packets: %1").arg(packetCount));
    m_blockedPacketsLabel->setText(QString("Blocked: %1").arg(blockedCount));
    m_allowedPacketsLabel->setText(QString("Allowed: %1").arg(allowedCount));

    // Update system usage bars
    m_cpuUsageBar->setValue(QRandomGenerator::global()->bounded(10, 50));
    m_memoryUsageBar->setValue(QRandomGenerator::global()->bounded(20, 80));
}

void DashboardView::onProtocolFilterChanged()
{
    // TODO: Update visualization based on protocol filter changes
    LOG_DEBUG("Protocol filter changed");
}

DashboardView::DashboardView(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::DashboardView)
{
    ui->setupUi(this);
    qDebug() << "DashboardView initialized.";
}

DashboardView::~DashboardView()
{
    delete ui;
    qDebug() << "DashboardView destroyed.";
}
