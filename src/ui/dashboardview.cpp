#include "dashboardview.h"
#include "widgets/networkvisualizationwidget.h"
#include "widgets/statisticswidget.h"
#include "widgets/packetflowwidget.h"
#include "widgets/protocolfilterwidget.h"
#include "widgets/animatedbutton.h"
#include "../utils/logger.h"
#include "../utils/animationhelper.h"
#include "../models/packetmodel.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QGroupBox>
#include <QProgressBar>
#include <QTimer>
#include <QPushButton>
#include <QSplitter>
#include <QTabWidget>
#include <QTableView>
#include <QHeaderView>
#include <QScrollArea>
#include <QFrame>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>
#include <QParallelAnimationGroup>
#include <QSequentialAnimationGroup>
#include <QEasingCurve>
#include <QRandomGenerator>
#include <QApplication>
#include <QStyle>
#include <QStyleOption>
#include <QPainter>
#include <QResizeEvent>
#include <QShowEvent>

DashboardView::DashboardView(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_topLayout(nullptr)
    , m_bottomLayout(nullptr)
    , m_statsGroup(nullptr)
    , m_packetsPerSecLabel(nullptr)
    , m_totalPacketsLabel(nullptr)
    , m_blockedPacketsLabel(nullptr)
    , m_allowedPacketsLabel(nullptr)
    , m_cpuUsageBar(nullptr)
    , m_memoryUsageBar(nullptr)
    , m_networkViz(nullptr)
    , m_packetFlow(nullptr)
    , m_protocolFilter(nullptr)
    , m_updateTimer(nullptr)
    , m_statisticsWidget(nullptr)
    , m_packetModel(nullptr)
    , m_animationGroup(nullptr)
    , m_isMonitoring(false)
    , m_totalPacketCount(0)
    , m_blockedPacketCount(0)
    , m_allowedPacketCount(0)
    , m_packetsPerSecond(0)
{
    // Initialize packet model
    m_packetModel = new PacketModel(this);

    setupUI();
    setupAnimations();
    setupConnections();

    // Setup update timer for real-time statistics
    m_updateTimer = new QTimer(this);
    connect(m_updateTimer, &QTimer::timeout, this, &DashboardView::updateStatistics);
    m_updateTimer->start(1000); // Update every second

    // Start with a welcome animation
    startWelcomeAnimation();

    LOG_INFO("DashboardView initialized with advanced features");
}

DashboardView::~DashboardView()
{
    if (m_updateTimer) {
        m_updateTimer->stop();
    }
    LOG_INFO("DashboardView destroyed");
}

void DashboardView::setupUI()
{
    setObjectName("DashboardView");
    setStyleSheet(
        "QWidget#DashboardView {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "        stop:0 #2d2d30, stop:1 #1e1e1e);"
        "}"
        "QGroupBox {"
        "    font-weight: bold;"
        "    border: 2px solid #555555;"
        "    border-radius: 8px;"
        "    margin-top: 10px;"
        "    padding-top: 10px;"
        "    background-color: rgba(60, 60, 60, 180);"
        "}"
        "QGroupBox::title {"
        "    subcontrol-origin: margin;"
        "    left: 10px;"
        "    padding: 0 8px 0 8px;"
        "    color: #ffffff;"
        "}"
    );

    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(15, 15, 15, 15);
    m_mainLayout->setSpacing(15);

    // Create main content with smooth animations
    setupStatisticsPanel();
    setupVisualizationPanel();
    setupControlPanel();

    // Add stretch to push everything to top
    m_mainLayout->addStretch();
}

void DashboardView::setupStatisticsPanel()
{
    m_statsGroup = new QGroupBox("📊 Real-Time Network Statistics", this);
    m_statsGroup->setMinimumHeight(120);

    QHBoxLayout *statsMainLayout = new QHBoxLayout(m_statsGroup);

    // Create statistics widget with charts
    m_statisticsWidget = new StatisticsWidget(this);
    m_statisticsWidget->setMinimumSize(300, 80);

    // Create info panel with animated labels
    QWidget *infoPanel = new QWidget();
    QGridLayout *infoLayout = new QGridLayout(infoPanel);

    // Packet statistics with animated counters
    m_packetsPerSecLabel = createAnimatedLabel("⚡ Packets/sec: 0", "#00ff00");
    m_totalPacketsLabel = createAnimatedLabel("📦 Total: 0", "#ffffff");
    m_blockedPacketsLabel = createAnimatedLabel("🚫 Blocked: 0", "#ff4444");
    m_allowedPacketsLabel = createAnimatedLabel("✅ Allowed: 0", "#44ff44");

    // System resource indicators
    QLabel *cpuLabel = new QLabel("🖥️ CPU:");
    cpuLabel->setStyleSheet("color: #cccccc; font-weight: bold;");
    m_cpuUsageBar = createStyledProgressBar("#4CAF50");

    QLabel *memoryLabel = new QLabel("💾 Memory:");
    memoryLabel->setStyleSheet("color: #cccccc; font-weight: bold;");
    m_memoryUsageBar = createStyledProgressBar("#2196F3");

    // Network status indicator
    QLabel *statusLabel = new QLabel("🌐 Status:");
    statusLabel->setStyleSheet("color: #cccccc; font-weight: bold;");
    m_networkStatusLabel = new QLabel("Initializing...");
    m_networkStatusLabel->setStyleSheet(
        "color: #ffaa00; font-weight: bold; "
        "background-color: rgba(255, 170, 0, 30); "
        "border-radius: 4px; padding: 4px 8px;"
    );

    // Layout the info panel
    infoLayout->addWidget(m_packetsPerSecLabel, 0, 0);
    infoLayout->addWidget(m_totalPacketsLabel, 0, 1);
    infoLayout->addWidget(m_blockedPacketsLabel, 0, 2);
    infoLayout->addWidget(m_allowedPacketsLabel, 0, 3);

    infoLayout->addWidget(cpuLabel, 1, 0);
    infoLayout->addWidget(m_cpuUsageBar, 1, 1);
    infoLayout->addWidget(memoryLabel, 1, 2);
    infoLayout->addWidget(m_memoryUsageBar, 1, 3);

    infoLayout->addWidget(statusLabel, 2, 0);
    infoLayout->addWidget(m_networkStatusLabel, 2, 1, 1, 3);

    // Add to main stats layout
    statsMainLayout->addWidget(m_statisticsWidget, 1);
    statsMainLayout->addWidget(infoPanel, 2);

    m_mainLayout->addWidget(m_statsGroup);
}

void DashboardView::setupVisualizationPanel()
{
    // Create tabbed visualization area
    QTabWidget *vizTabs = new QTabWidget(this);
    vizTabs->setStyleSheet(
        "QTabWidget::pane {"
        "    border: 2px solid #555555;"
        "    border-radius: 8px;"
        "    background-color: rgba(45, 45, 48, 200);"
        "}"
        "QTabBar::tab {"
        "    background-color: #3c3c3c;"
        "    color: #ffffff;"
        "    padding: 8px 16px;"
        "    margin-right: 2px;"
        "    border-top-left-radius: 6px;"
        "    border-top-right-radius: 6px;"
        "}"
        "QTabBar::tab:selected {"
        "    background-color: #007acc;"
        "    font-weight: bold;"
        "}"
        "QTabBar::tab:hover {"
        "    background-color: #4a4a4a;"
        "}"
    );

    // Network Topology Tab
    QWidget *topologyTab = new QWidget();
    QHBoxLayout *topologyLayout = new QHBoxLayout(topologyTab);

    // Main network visualization
    m_networkViz = new NetworkVisualizationWidget(this);
    m_networkViz->setMinimumSize(600, 400);

    // Control panel with enhanced features
    QWidget *controlPanel = createVisualizationControlPanel();
    controlPanel->setMaximumWidth(280);
    controlPanel->setMinimumWidth(250);

    topologyLayout->addWidget(m_networkViz, 3);
    topologyLayout->addWidget(controlPanel, 1);

    // Packet Flow Tab
    QWidget *packetTab = new QWidget();
    QVBoxLayout *packetLayout = new QVBoxLayout(packetTab);

    m_packetFlow = new PacketFlowWidget(this);
    m_packetFlow->setMinimumHeight(300);

    // Packet flow controls
    QHBoxLayout *flowControls = new QHBoxLayout();
    QPushButton *playBtn = new QPushButton("▶️ Play");
    QPushButton *pauseBtn = new QPushButton("⏸️ Pause");
    QPushButton *resetBtn = new QPushButton("🔄 Reset");

    playBtn->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px 16px; border-radius: 4px; }");
    pauseBtn->setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; padding: 8px 16px; border-radius: 4px; }");
    resetBtn->setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; padding: 8px 16px; border-radius: 4px; }");

    flowControls->addWidget(playBtn);
    flowControls->addWidget(pauseBtn);
    flowControls->addWidget(resetBtn);
    flowControls->addStretch();

    packetLayout->addWidget(m_packetFlow);
    packetLayout->addLayout(flowControls);

    // Add tabs
    vizTabs->addTab(topologyTab, "🌐 Network Topology");
    vizTabs->addTab(packetTab, "📊 Packet Flow");

    m_mainLayout->addWidget(vizTabs, 1);
}

void DashboardView::setupControlPanel()
{
    QGroupBox *controlGroup = new QGroupBox("🎛️ Dashboard Controls", this);
    controlGroup->setMaximumHeight(80);

    QHBoxLayout *controlLayout = new QHBoxLayout(controlGroup);

    // Create animated control buttons
    m_startButton = new AnimatedButton("🚀 Start Monitoring", this);
    m_startButton->setStyleSheet(
        "AnimatedButton {"
        "    background-color: #4CAF50;"
        "    color: white;"
        "    font-weight: bold;"
        "    padding: 12px 24px;"
        "    border-radius: 6px;"
        "    border: none;"
        "}"
        "AnimatedButton:hover {"
        "    background-color: #45a049;"
        "}"
        "AnimatedButton:pressed {"
        "    background-color: #3d8b40;"
        "}"
    );

    m_stopButton = new AnimatedButton("⏹️ Stop Monitoring", this);
    m_stopButton->setStyleSheet(
        "AnimatedButton {"
        "    background-color: #f44336;"
        "    color: white;"
        "    font-weight: bold;"
        "    padding: 12px 24px;"
        "    border-radius: 6px;"
        "    border: none;"
        "}"
        "AnimatedButton:hover {"
        "    background-color: #da190b;"
        "}"
        "AnimatedButton:pressed {"
        "    background-color: #c1170a;"
        "}"
    );
    m_stopButton->setEnabled(false);

    m_clearButton = new AnimatedButton("🗑️ Clear Data", this);
    m_clearButton->setStyleSheet(
        "AnimatedButton {"
        "    background-color: #FF9800;"
        "    color: white;"
        "    font-weight: bold;"
        "    padding: 12px 24px;"
        "    border-radius: 6px;"
        "    border: none;"
        "}"
        "AnimatedButton:hover {"
        "    background-color: #e68900;"
        "}"
        "AnimatedButton:pressed {"
        "    background-color: #cc7a00;"
        "}"
    );

    m_exportButton = new AnimatedButton("💾 Export Report", this);
    m_exportButton->setStyleSheet(
        "AnimatedButton {"
        "    background-color: #2196F3;"
        "    color: white;"
        "    font-weight: bold;"
        "    padding: 12px 24px;"
        "    border-radius: 6px;"
        "    border: none;"
        "}"
        "AnimatedButton:hover {"
        "    background-color: #1976D2;"
        "}"
        "AnimatedButton:pressed {"
        "    background-color: #1565C0;"
        "}"
    );

    // Add status indicator
    QLabel *statusIndicator = new QLabel("●");
    statusIndicator->setStyleSheet("color: #ffaa00; font-size: 20px;");
    statusIndicator->setToolTip("System Status");

    controlLayout->addWidget(m_startButton);
    controlLayout->addWidget(m_stopButton);
    controlLayout->addWidget(m_clearButton);
    controlLayout->addWidget(m_exportButton);
    controlLayout->addStretch();
    controlLayout->addWidget(new QLabel("Status:"));
    controlLayout->addWidget(statusIndicator);

    m_mainLayout->addWidget(controlGroup);
}

void DashboardView::updateStatistics()
{
    // TODO: Get real statistics from packet capture engine
    static int packetCount = 0;
    static int packetsPerSec = 0;
    static int blockedCount = 0;
    static int allowedCount = 0;

    // Simulate some activity
    packetCount += QRandomGenerator::global()->bounded(0, 10);
    packetsPerSec = QRandomGenerator::global()->bounded(0, 100);
    blockedCount += QRandomGenerator::global()->bounded(0, 2);
    allowedCount = packetCount - blockedCount;

    m_packetsPerSecLabel->setText(QString("Packets/sec: %1").arg(packetsPerSec));
    m_totalPacketsLabel->setText(QString("Total Packets: %1").arg(packetCount));
    m_blockedPacketsLabel->setText(QString("Blocked: %1").arg(blockedCount));
    m_allowedPacketsLabel->setText(QString("Allowed: %1").arg(allowedCount));

    // Update system usage bars
    m_cpuUsageBar->setValue(QRandomGenerator::global()->bounded(10, 50));
    m_memoryUsageBar->setValue(QRandomGenerator::global()->bounded(20, 80));
}

// Helper methods for UI creation
QLabel* DashboardView::createAnimatedLabel(const QString &text, const QString &color)
{
    QLabel *label = new QLabel(text);
    label->setStyleSheet(QString(
        "QLabel {"
        "    color: %1;"
        "    font-weight: bold;"
        "    font-size: 14px;"
        "    padding: 4px 8px;"
        "    background-color: rgba(255, 255, 255, 20);"
        "    border-radius: 4px;"
        "}"
    ).arg(color));

    // Add fade-in animation
    QGraphicsOpacityEffect *effect = new QGraphicsOpacityEffect(label);
    label->setGraphicsEffect(effect);

    QPropertyAnimation *animation = new QPropertyAnimation(effect, "opacity");
    animation->setDuration(500);
    animation->setStartValue(0.0);
    animation->setEndValue(1.0);
    animation->setEasingCurve(QEasingCurve::InOutQuad);
    animation->start(QAbstractAnimation::DeleteWhenStopped);

    return label;
}

QProgressBar* DashboardView::createStyledProgressBar(const QString &color)
{
    QProgressBar *bar = new QProgressBar();
    bar->setRange(0, 100);
    bar->setValue(0);
    bar->setTextVisible(true);
    bar->setStyleSheet(QString(
        "QProgressBar {"
        "    border: 2px solid #555555;"
        "    border-radius: 6px;"
        "    text-align: center;"
        "    background-color: #2d2d30;"
        "    color: white;"
        "    font-weight: bold;"
        "}"
        "QProgressBar::chunk {"
        "    background-color: %1;"
        "    border-radius: 4px;"
        "}"
    ).arg(color));

    return bar;
}

QWidget* DashboardView::createVisualizationControlPanel()
{
    QWidget *panel = new QWidget();
    panel->setStyleSheet(
        "QWidget {"
        "    background-color: rgba(60, 60, 60, 150);"
        "    border-radius: 8px;"
        "}"
    );

    QVBoxLayout *layout = new QVBoxLayout(panel);

    // Protocol filter section
    QGroupBox *filterGroup = new QGroupBox("🔍 Protocol Filters");
    QVBoxLayout *filterLayout = new QVBoxLayout(filterGroup);

    m_protocolFilter = new ProtocolFilterWidget(this);
    filterLayout->addWidget(m_protocolFilter);

    // Visualization controls
    QGroupBox *vizGroup = new QGroupBox("🎨 Visualization");
    QVBoxLayout *vizLayout = new QVBoxLayout(vizGroup);

    QPushButton *zoomInBtn = new QPushButton("🔍+ Zoom In");
    QPushButton *zoomOutBtn = new QPushButton("🔍- Zoom Out");
    QPushButton *resetViewBtn = new QPushButton("🎯 Reset View");
    QPushButton *fullscreenBtn = new QPushButton("⛶ Fullscreen");

    QString buttonStyle =
        "QPushButton {"
        "    background-color: #4a4a4a;"
        "    color: white;"
        "    border: none;"
        "    padding: 8px 12px;"
        "    border-radius: 4px;"
        "    font-weight: bold;"
        "}"
        "QPushButton:hover {"
        "    background-color: #5a5a5a;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #3a3a3a;"
        "}";

    zoomInBtn->setStyleSheet(buttonStyle);
    zoomOutBtn->setStyleSheet(buttonStyle);
    resetViewBtn->setStyleSheet(buttonStyle);
    fullscreenBtn->setStyleSheet(buttonStyle);

    vizLayout->addWidget(zoomInBtn);
    vizLayout->addWidget(zoomOutBtn);
    vizLayout->addWidget(resetViewBtn);
    vizLayout->addWidget(fullscreenBtn);

    // Layout options
    QGroupBox *layoutGroup = new QGroupBox("📐 Layout");
    QVBoxLayout *layoutGroupLayout = new QVBoxLayout(layoutGroup);

    QComboBox *layoutCombo = new QComboBox();
    layoutCombo->addItems({"Circular", "Grid", "Force-Directed", "Hierarchical"});
    layoutCombo->setStyleSheet(
        "QComboBox {"
        "    background-color: #3c3c3c;"
        "    color: white;"
        "    border: 1px solid #555555;"
        "    padding: 6px;"
        "    border-radius: 4px;"
        "}"
    );

    layoutGroupLayout->addWidget(layoutCombo);

    // Add all groups to main layout
    layout->addWidget(filterGroup);
    layout->addWidget(vizGroup);
    layout->addWidget(layoutGroup);
    layout->addStretch();

    return panel;
}

void DashboardView::setupAnimations()
{
    m_animationGroup = new QParallelAnimationGroup(this);

    // Create fade animation for the entire widget
    QGraphicsOpacityEffect *opacityEffect = new QGraphicsOpacityEffect(this);
    setGraphicsEffect(opacityEffect);

    m_fadeAnimation = new QPropertyAnimation(opacityEffect, "opacity");
    m_fadeAnimation->setDuration(1000);
    m_fadeAnimation->setEasingCurve(QEasingCurve::InOutQuad);

    m_animationGroup->addAnimation(m_fadeAnimation);
}

void DashboardView::setupConnections()
{
    // Connect control buttons
    connect(m_startButton, &AnimatedButton::clicked, this, &DashboardView::startMonitoring);
    connect(m_stopButton, &AnimatedButton::clicked, this, &DashboardView::stopMonitoring);
    connect(m_clearButton, &AnimatedButton::clicked, this, &DashboardView::clearData);
    connect(m_exportButton, &AnimatedButton::clicked, this, &DashboardView::exportData);

    // Connect packet model signals
    connect(m_packetModel, &PacketModel::packetAdded, this, &DashboardView::onPacketReceived);
    connect(m_packetModel, &PacketModel::statisticsChanged, this, &DashboardView::animateStatisticsUpdate);

    // Connect network visualization signals
    connect(m_networkViz, &NetworkVisualizationWidget::nodeClicked, this, &DashboardView::onNetworkNodeClicked);

    // Connect protocol filter
    if (m_protocolFilter) {
        connect(m_protocolFilter, &ProtocolFilterWidget::filterChanged, this, &DashboardView::onProtocolFilterChanged);
    }
}

void DashboardView::startWelcomeAnimation()
{
    if (m_fadeAnimation) {
        m_fadeAnimation->setStartValue(0.0);
        m_fadeAnimation->setEndValue(1.0);
        m_fadeAnimation->start();
    }
}

// Slot implementations
void DashboardView::startMonitoring()
{
    if (m_isMonitoring) return;

    m_isMonitoring = true;
    m_startButton->setEnabled(false);
    m_stopButton->setEnabled(true);

    // Update status
    if (m_networkStatusLabel) {
        m_networkStatusLabel->setText("🟢 Active");
        m_networkStatusLabel->setStyleSheet(
            "color: #44ff44; font-weight: bold; "
            "background-color: rgba(68, 255, 68, 30); "
            "border-radius: 4px; padding: 4px 8px;"
        );
    }

    // Start network visualization
    if (m_networkViz) {
        m_networkViz->startAnimation();
    }

    emit monitoringStarted();
    LOG_INFO("Network monitoring started");
}

void DashboardView::stopMonitoring()
{
    if (!m_isMonitoring) return;

    m_isMonitoring = false;
    m_startButton->setEnabled(true);
    m_stopButton->setEnabled(false);

    // Update status
    if (m_networkStatusLabel) {
        m_networkStatusLabel->setText("🔴 Stopped");
        m_networkStatusLabel->setStyleSheet(
            "color: #ff4444; font-weight: bold; "
            "background-color: rgba(255, 68, 68, 30); "
            "border-radius: 4px; padding: 4px 8px;"
        );
    }

    // Stop network visualization
    if (m_networkViz) {
        m_networkViz->stopAnimation();
    }

    emit monitoringStopped();
    LOG_INFO("Network monitoring stopped");
}

void DashboardView::onProtocolFilterChanged()
{
    if (!m_protocolFilter || !m_networkViz) return;

    // Get selected protocols from filter widget
    QList<ProtocolType> selectedProtocols; // TODO: Get from m_protocolFilter

    // Update network visualization
    m_networkViz->setProtocolFilter(selectedProtocols);

    // Update packet model filter
    m_packetModel->setProtocolFilter(selectedProtocols);

    LOG_DEBUG("Protocol filter changed");
}
