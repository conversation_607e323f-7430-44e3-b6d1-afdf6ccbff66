#include "mainwindow.h"
#include "dashboardview.h"
#include "rulemanagerview.h"
#include "alertview.h"
#include "reportview.h"
#include "settingsview.h"
#include "../utils/logger.h"
#include "../utils/configmanager.h"
#include "../utils/thememanager.h"

#include <QApplication>
#include <QMenuBar>
#include <QStatusBar>
#include <QToolBar>
#include <QAction>
#include <QMessageBox>
#include <QCloseEvent>
#include <QSplitter>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralStack(nullptr)
    , m_dashboardView(nullptr)
    , m_ruleView(nullptr)
    , m_alertView(nullptr)
    , m_reportView(nullptr)
    , m_settingsView(nullptr)
    , m_mainToolBar(nullptr)
{
    setWindowTitle("QT Firewall - Network Security Monitor");
    setWindowIcon(QIcon(":/icons/firewall.png"));

    // Set minimum size
    setMinimumSize(1200, 800);

    // Restore window geometry
    QByteArray geometry = ConfigManager::instance().getValue("ui/window_geometry").toByteArray();
    if (!geometry.isEmpty()) {
        restoreGeometry(geometry);
    } else {
        resize(1400, 900);
    }

    QByteArray state = ConfigManager::instance().getValue("ui/window_state").toByteArray();
    if (!state.isEmpty()) {
        restoreState(state);
    }

    setupUI();
    setupMenuBar();
    setupToolBar();
    setupStatusBar();
    setupConnections();

    // Start with dashboard view
    showDashboard();

    LOG_INFO("MainWindow initialized");
}

MainWindow::~MainWindow()
{
    // Save window geometry
    ConfigManager::instance().setValue("ui/window_geometry", saveGeometry());
    ConfigManager::instance().setValue("ui/window_state", saveState());
    ConfigManager::instance().sync();

    LOG_INFO("MainWindow destroyed");
}

void MainWindow::setupUI()
{
    // Create central stacked widget
    m_centralStack = new QStackedWidget(this);
    setCentralWidget(m_centralStack);

    // Create views
    m_dashboardView = new DashboardView(this);
    m_ruleView = new RuleManagerView(this);
    m_alertView = new AlertView(this);
    m_reportView = new ReportView(this);
    m_settingsView = new SettingsView(this);

    // Add views to stack
    m_centralStack->addWidget(m_dashboardView);
    m_centralStack->addWidget(m_ruleView);
    m_centralStack->addWidget(m_alertView);
    m_centralStack->addWidget(m_reportView);
    m_centralStack->addWidget(m_settingsView);
}

void MainWindow::setupMenuBar()
{
    // File menu
    QMenu *fileMenu = menuBar()->addMenu("&File");

    QAction *newRuleAction = fileMenu->addAction("&New Rule");
    newRuleAction->setShortcut(QKeySequence::New);
    newRuleAction->setIcon(QIcon(":/icons/rules.png"));
    connect(newRuleAction, &QAction::triggered, this, &MainWindow::showRuleManager);

    QAction *saveConfigAction = fileMenu->addAction("&Save Configuration");
    saveConfigAction->setShortcut(QKeySequence::Save);
    connect(saveConfigAction, &QAction::triggered, [this]() {
        ConfigManager::instance().sync();
        statusBar()->showMessage("Configuration saved", 2000);
    });

    fileMenu->addSeparator();

    QAction *exportAction = fileMenu->addAction("&Export Report");
    exportAction->setShortcut(QKeySequence("Ctrl+E"));
    exportAction->setIcon(QIcon(":/icons/reports.png"));
    connect(exportAction, &QAction::triggered, this, &MainWindow::showReports);

    fileMenu->addSeparator();

    QAction *exitAction = fileMenu->addAction("E&xit");
    exitAction->setShortcut(QKeySequence::Quit);
    connect(exitAction, &QAction::triggered, this, &QWidget::close);

    // View menu
    QMenu *viewMenu = menuBar()->addMenu("&View");

    QAction *dashboardAction = viewMenu->addAction("&Dashboard");
    dashboardAction->setShortcut(QKeySequence("F1"));
    dashboardAction->setIcon(QIcon(":/icons/dashboard.png"));
    connect(dashboardAction, &QAction::triggered, this, &MainWindow::showDashboard);

    QAction *rulesAction = viewMenu->addAction("&Rules");
    rulesAction->setShortcut(QKeySequence("F2"));
    rulesAction->setIcon(QIcon(":/icons/rules.png"));
    connect(rulesAction, &QAction::triggered, this, &MainWindow::showRuleManager);

    QAction *alertsAction = viewMenu->addAction("&Alerts");
    alertsAction->setShortcut(QKeySequence("F3"));
    alertsAction->setIcon(QIcon(":/icons/alerts.png"));
    connect(alertsAction, &QAction::triggered, this, &MainWindow::showAlerts);

    QAction *reportsAction = viewMenu->addAction("Re&ports");
    reportsAction->setShortcut(QKeySequence("F4"));
    reportsAction->setIcon(QIcon(":/icons/reports.png"));
    connect(reportsAction, &QAction::triggered, this, &MainWindow::showReports);

    viewMenu->addSeparator();

    QAction *refreshAction = viewMenu->addAction("&Refresh");
    refreshAction->setShortcut(QKeySequence::Refresh);
    connect(refreshAction, &QAction::triggered, [this]() {
        // Refresh current view
        statusBar()->showMessage("Refreshing...", 1000);
    });

    QAction *fullscreenAction = viewMenu->addAction("&Fullscreen");
    fullscreenAction->setShortcut(QKeySequence("F11"));
    fullscreenAction->setCheckable(true);
    connect(fullscreenAction, &QAction::triggered, [this](bool checked) {
        if (checked) {
            showFullScreen();
        } else {
            showNormal();
        }
    });
}
void MainWindow::setupToolBar()
{
    m_mainToolBar = addToolBar("Main");
    m_mainToolBar->setToolButtonStyle(Qt::ToolButtonTextUnderIcon);

    // Dashboard action
    QAction *dashboardAction = m_mainToolBar->addAction(QIcon(":/icons/dashboard.png"), "Dashboard");
    connect(dashboardAction, &QAction::triggered, this, &MainWindow::showDashboard);

    // Rules action
    QAction *rulesAction = m_mainToolBar->addAction(QIcon(":/icons/rules.png"), "Rules");
    connect(rulesAction, &QAction::triggered, this, &MainWindow::showRuleManager);

    // Alerts action
    QAction *alertsAction = m_mainToolBar->addAction(QIcon(":/icons/alerts.png"), "Alerts");
    connect(alertsAction, &QAction::triggered, this, &MainWindow::showAlerts);

    // Reports action
    QAction *reportsAction = m_mainToolBar->addAction(QIcon(":/icons/reports.png"), "Reports");
    connect(reportsAction, &QAction::triggered, this, &MainWindow::showReports);

    m_mainToolBar->addSeparator();

    // Settings action
    QAction *settingsAction = m_mainToolBar->addAction(QIcon(":/icons/settings.png"), "Settings");
    connect(settingsAction, &QAction::triggered, this, &MainWindow::showSettings);
}

void MainWindow::setupStatusBar()
{
    statusBar()->showMessage("Ready");

    // Add permanent widgets to status bar
    QLabel *statusLabel = new QLabel("Monitoring: Active");
    statusLabel->setStyleSheet("QLabel { color: green; font-weight: bold; }");
    statusBar()->addPermanentWidget(statusLabel);

    QLabel *packetCountLabel = new QLabel("Packets: 0");
    statusBar()->addPermanentWidget(packetCountLabel);

    QLabel *ruleCountLabel = new QLabel("Rules: 0");
    statusBar()->addPermanentWidget(ruleCountLabel);
}

void MainWindow::setupConnections()
{
    // Connect theme manager
    connect(&ThemeManager::instance(), &ThemeManager::themeChanged,
            [this](const QString &themeName) {
                statusBar()->showMessage(QString("Theme changed to: %1").arg(themeName), 2000);
            });
}

void MainWindow::showDashboard()
{
    m_centralStack->setCurrentWidget(m_dashboardView);
    statusBar()->showMessage("Dashboard view active");
    LOG_DEBUG("Switched to Dashboard view");
}

void MainWindow::showRuleManager()
{
    m_centralStack->setCurrentWidget(m_ruleView);
    statusBar()->showMessage("Rule management view active");
    LOG_DEBUG("Switched to Rule management view");
}

void MainWindow::showAlerts()
{
    m_centralStack->setCurrentWidget(m_alertView);
    statusBar()->showMessage("Alerts view active");
    LOG_DEBUG("Switched to Alerts view");
}

void MainWindow::showReports()
{
    m_centralStack->setCurrentWidget(m_reportView);
    statusBar()->showMessage("Reports view active");
    LOG_DEBUG("Switched to Reports view");
}

void MainWindow::showSettings()
{
    m_centralStack->setCurrentWidget(m_settingsView);
    statusBar()->showMessage("Settings view active");
    LOG_DEBUG("Switched to Settings view");
}

void MainWindow::showAbout()
{
    QMessageBox::about(this, "About QT Firewall",
        "<h2>QT Firewall</h2>"
        "<p>Version 1.0.0</p>"
        "<p>Advanced Qt-based Firewall Application with Real-time Monitoring</p>"
        "<p>Built with Qt6 and modern C++ practices</p>"
        "<p>Features:</p>"
        "<ul>"
        "<li>Real-time packet monitoring</li>"
        "<li>Animated network visualization</li>"
        "<li>Advanced rule management</li>"
        "<li>Comprehensive reporting</li>"
        "<li>Cross-platform support</li>"
        "</ul>"
        "<p>Copyright © 2024 QT Firewall Team</p>");
}
