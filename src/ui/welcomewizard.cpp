#include "welcomewizard.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QCheckBox>
#include <QListWidget>
#include <QTextEdit>

WelcomeWizard::WelcomeWizard(QWidget *parent) : QWizard(parent)
{
    setWindowTitle("QT Firewall Setup Wizard");
    setWizardStyle(QWizard::ModernStyle);
    setOption(QWizard::HaveHelpButton, false);
    setupPages();
}

WelcomeWizard::~WelcomeWizard() = default;

void WelcomeWizard::setupPages()
{
    addPage(new WelcomePage(this));
    addPage(new InterfaceSelectionPage(this));
    addPage(new RuleTemplatePage(this));
    addPage(new CompletionPage(this));
}

// Welcome Page Implementation
WelcomePage::WelcomePage(QWidget *parent) : QWizardPage(parent)
{
    setTitle("Welcome to QT Firewall");
    setSubTitle("Advanced Network Security Monitoring");
    setupUI();
}

void WelcomePage::setupUI()
{
    QVBoxLayout *layout = new QVBoxLayout(this);

    QLabel *welcomeLabel = new QLabel(
        "<h2>Welcome to QT Firewall</h2>"
        "<p>This wizard will help you set up your firewall application for optimal network monitoring.</p>"
        "<p><b>Features:</b></p>"
        "<ul>"
        "<li>Real-time packet monitoring</li>"
        "<li>Animated network visualization</li>"
        "<li>Advanced rule management</li>"
        "<li>Comprehensive reporting</li>"
        "</ul>"
        "<p>Click Next to begin the setup process.</p>"
    );
    welcomeLabel->setWordWrap(true);
    layout->addWidget(welcomeLabel);
}

// Interface Selection Page
InterfaceSelectionPage::InterfaceSelectionPage(QWidget *parent) : QWizardPage(parent)
{
    setTitle("Network Interface Selection");
    setSubTitle("Choose which network interfaces to monitor");
    setupUI();
}

void InterfaceSelectionPage::setupUI()
{
    QVBoxLayout *layout = new QVBoxLayout(this);

    QLabel *infoLabel = new QLabel("Select the network interfaces you want to monitor:");
    layout->addWidget(infoLabel);

    // Placeholder interface list
    QCheckBox *eth0 = new QCheckBox("eth0 - Ethernet Interface");
    QCheckBox *wlan0 = new QCheckBox("wlan0 - Wireless Interface");
    QCheckBox *lo = new QCheckBox("lo - Loopback Interface");

    eth0->setChecked(true);
    wlan0->setChecked(true);

    layout->addWidget(eth0);
    layout->addWidget(wlan0);
    layout->addWidget(lo);
    layout->addStretch();
}

void InterfaceSelectionPage::loadNetworkInterfaces()
{
    // TODO: Load actual network interfaces
}

// Rule Template Page
RuleTemplatePage::RuleTemplatePage(QWidget *parent) : QWizardPage(parent)
{
    setTitle("Rule Templates");
    setSubTitle("Choose initial firewall rule templates");
    setupUI();
}

void RuleTemplatePage::setupUI()
{
    QVBoxLayout *layout = new QVBoxLayout(this);

    QLabel *infoLabel = new QLabel("Select rule templates to start with:");
    layout->addWidget(infoLabel);

    QCheckBox *blockAll = new QCheckBox("Block All Incoming TCP");
    QCheckBox *allowHTTP = new QCheckBox("Allow HTTP/HTTPS Traffic");
    QCheckBox *allowSSH = new QCheckBox("Allow SSH Access");
    QCheckBox *blockP2P = new QCheckBox("Block P2P Traffic");

    allowHTTP->setChecked(true);

    layout->addWidget(blockAll);
    layout->addWidget(allowHTTP);
    layout->addWidget(allowSSH);
    layout->addWidget(blockP2P);
    layout->addStretch();
}

// Completion Page
CompletionPage::CompletionPage(QWidget *parent) : QWizardPage(parent)
{
    setTitle("Setup Complete");
    setSubTitle("Your firewall is ready to use");
    setupUI();
}

void CompletionPage::setupUI()
{
    QVBoxLayout *layout = new QVBoxLayout(this);

    QLabel *completionLabel = new QLabel(
        "<h3>Setup Complete!</h3>"
        "<p>Your QT Firewall has been configured successfully.</p>"
        "<p><b>Next Steps:</b></p>"
        "<ul>"
        "<li>Start monitoring network traffic</li>"
        "<li>Create custom firewall rules</li>"
        "<li>View real-time network visualization</li>"
        "<li>Generate security reports</li>"
        "</ul>"
        "<p>Click Finish to start using the application.</p>"
    );
    completionLabel->setWordWrap(true);
    layout->addWidget(completionLabel);
}

WelcomeWizard::WelcomeWizard(QWidget *parent)
    : QWizard(parent)
    , ui(new Ui::WelcomeWizard)
{
    ui->setupUi(this);
    qDebug() << "WelcomeWizard initialized.";
}

WelcomeWizard::~WelcomeWizard()
{
    delete ui;
    qDebug() << "WelcomeWizard destroyed.";
}
