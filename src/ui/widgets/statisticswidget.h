#ifndef STATISTICSWIDGET_H
#define STATISTICSWIDGET_H

#include <QWidget>
#include <QtCharts/QChartView>
#include <QtCharts/QLineSeries>
#include <QtCharts/QAreaSeries>
#include <QtCharts/QValueAxis>
#include <QtCharts/QDateTimeAxis>
#include <QTimer>
#include <QDateTime>

// Removed: using namespace QtCharts;

/**
 * @brief Real-time statistics widget with animated charts
 */
class StatisticsWidget : public QWidget
{
    Q_OBJECT

public:
    explicit StatisticsWidget(QWidget *parent = nullptr);
    ~StatisticsWidget();

public slots:
    void updatePacketRate(int packetsPerSecond);
    void updateTotalPackets(quint64 totalPackets);
    void updateBlockedPackets(quint64 blockedPackets);
    void clearData();

private slots:
    void updateCharts();

private:
    void setupUI();
    void setupCharts();

    QtCharts::QChartView *m_chartView;
    QtCharts::QChart *m_chart;
    QtCharts::QLineSeries *m_packetRateSeries;
    QtCharts::QAreaSeries *m_trafficAreaSeries;
    QtCharts::QValueAxis *m_axisY;
    QtCharts::QDateTimeAxis *m_axisX;
    QTimer *m_updateTimer;

    // Data tracking
    QList<QPair<QDateTime, int>> m_packetRateData;
    int m_maxDataPoints;
};

#endif // STATISTICSWIDGET_H
