#ifndef STATISTICSWIDGET_H
#define STATISTICSWIDGET_H

#include <QWidget>
#include <QTimer>
#include <QDateTime>
#include <QLabel>

/**
 * @brief Real-time statistics widget (simplified version without QtCharts)
 */
class StatisticsWidget : public QWidget
{
    Q_OBJECT

public:
    explicit StatisticsWidget(QWidget *parent = nullptr);
    ~StatisticsWidget();

public slots:
    void updatePacketRate(int packetsPerSecond);
    void updateTotalPackets(quint64 totalPackets);
    void updateBlockedPackets(quint64 blockedPackets);
    void clearData();

private slots:
    void updateCharts();

private:
    void setupUI();

    QLabel *m_placeholderLabel;
    QTimer *m_updateTimer;

    // Data tracking
    QList<QPair<QDateTime, int>> m_packetRateData;
    int m_maxDataPoints;
};

#endif // STATISTICSWIDGET_H
