#ifndef STATISTICSWIDGET_H
#define STATISTICSWIDGET_H

#include <QWidget>
#include <QTimer>
#include <QDateTime>

// Forward declarations for Qt Charts
QT_BEGIN_NAMESPACE
class QChartView;
class QChart;
class QLineSeries;
class QAreaSeries;
class QValueAxis;
class QDateTimeAxis;
QT_END_NAMESPACE

/**
 * @brief Real-time statistics widget with animated charts
 */
class StatisticsWidget : public QWidget
{
    Q_OBJECT

public:
    explicit StatisticsWidget(QWidget *parent = nullptr);
    ~StatisticsWidget();

public slots:
    void updatePacketRate(int packetsPerSecond);
    void updateTotalPackets(quint64 totalPackets);
    void updateBlockedPackets(quint64 blockedPackets);
    void clearData();

private slots:
    void updateCharts();

private:
    void setupUI();
    void setupCharts();

    QChartView *m_chartView;
    QChart *m_chart;
    QLineSeries *m_packetRateSeries;
    QAreaSeries *m_trafficAreaSeries;
    QValueAxis *m_axisY;
    QDateTimeAxis *m_axisX;
    QTimer *m_updateTimer;

    // Data tracking
    QList<QPair<QDateTime, int>> m_packetRateData;
    int m_maxDataPoints;
};

#endif // STATISTICSWIDGET_H
