#ifndef NETWORKVISUALIZATIONWIDGET_H
#define NETWORKVISUALIZATIONWIDGET_H

#include <QWidget>
#include <QGraphicsView>
#include <QGraphicsScene>
#include <QGraphicsItem>
#include <QTimer>
#include <QPropertyAnimation>
#include <QParallelAnimationGroup>
#include <QSequentialAnimationGroup>
#include <QEasingCurve>
#include <QMouseEvent>
#include <QWheelEvent>
#include <QPainter>
#include <QStyleOption>
#include <QRandomGenerator>

#include "../../models/packetmodel.h"

class NetworkNode;
class NetworkLink;
class PacketAnimation;

/**
 * @brief Advanced network visualization widget with animated packet flows
 *
 * This widget provides an interactive network topology visualization with:
 * - Animated packet flows between nodes
 * - Real-time network statistics
 * - Interactive zoom and pan
 * - Protocol-based color coding
 * - Node and link hover effects
 * - Customizable layout algorithms
 *
 * The visualization directly implements the user journey requirements for
 * animated network topology with live packet flows and interactive elements.
 */
class NetworkVisualizationWidget : public QGraphicsView
{
    Q_OBJECT
    Q_PROPERTY(qreal zoomFactor READ zoomFactor WRITE setZoomFactor)

public:
    explicit NetworkVisualizationWidget(QWidget *parent = nullptr);
    ~NetworkVisualizationWidget();

    // Zoom control
    qreal zoomFactor() const { return m_zoomFactor; }
    void setZoomFactor(qreal factor);

    // Animation control
    void setAnimationEnabled(bool enabled);
    bool isAnimationEnabled() const { return m_animationEnabled; }

    // Protocol filtering
    void setProtocolFilter(const QList<ProtocolType> &protocols);
    QList<ProtocolType> getProtocolFilter() const { return m_protocolFilter; }

    // Layout control
    enum LayoutType {
        CircularLayout,
        GridLayout,
        ForceDirectedLayout,
        HierarchicalLayout
    };
    void setLayoutType(LayoutType type);
    LayoutType getLayoutType() const { return m_layoutType; }

public slots:
    void addPacket(const PacketInfo &packet);
    void clearPackets();
    void updateNetworkTopology();
    void resetView();
    void zoomIn();
    void zoomOut();
    void zoomToFit();
    void startAnimation();
    void stopAnimation();
    void pauseAnimation();

signals:
    void nodeClicked(const QString &nodeId);
    void linkClicked(const QString &linkId);
    void packetClicked(const PacketInfo &packet);
    void zoomChanged(qreal factor);

protected:
    void wheelEvent(QWheelEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;
    void showEvent(QShowEvent *event) override;

private slots:
    void onAnimationTimer();
    void onNodeHovered(bool hovered);
    void onLinkHovered(bool hovered);
    void onPacketAnimationFinished();

private:
    void setupScene();
    void setupAnimations();
    void createNetworkTopology();
    void updateNodePositions();
    void animatePacketFlow(const PacketInfo &packet);
    void updateStatistics();

    // Layout algorithms
    void applyCircularLayout();
    void applyGridLayout();
    void applyForceDirectedLayout();
    void applyHierarchicalLayout();

    // Utility methods
    QColor getProtocolColor(ProtocolType protocol) const;
    QString getNodeId(const QHostAddress &address) const;
    NetworkNode* findOrCreateNode(const QString &nodeId, const QHostAddress &address);
    NetworkLink* findOrCreateLink(const QString &sourceId, const QString &destId);
    void cleanupOldAnimations();

    QGraphicsScene *m_scene;
    QTimer *m_animationTimer;

    // Visualization state
    qreal m_zoomFactor;
    bool m_animationEnabled;
    LayoutType m_layoutType;
    QList<ProtocolType> m_protocolFilter;

    // Network elements
    QMap<QString, NetworkNode*> m_nodes;
    QMap<QString, NetworkLink*> m_links;
    QList<PacketAnimation*> m_activeAnimations;

    // Mouse interaction
    bool m_dragging;
    QPointF m_lastPanPoint;

    // Animation groups
    QParallelAnimationGroup *m_packetAnimationGroup;
    QPropertyAnimation *m_zoomAnimation;

    // Statistics
    int m_totalPackets;
    int m_packetsPerSecond;
    QDateTime m_lastStatsUpdate;

    // Configuration
    static const int MAX_ACTIVE_ANIMATIONS = 100;
    static const int ANIMATION_DURATION = 2000; // 2 seconds
    static const qreal MIN_ZOOM = 0.1;
    static const qreal MAX_ZOOM = 5.0;
    static const qreal ZOOM_STEP = 1.2;
};

/**
 * @brief Represents a network node in the visualization
 */
class NetworkNode : public QGraphicsItem
{
public:
    explicit NetworkNode(const QString &nodeId, const QHostAddress &address, QGraphicsItem *parent = nullptr);

    QString nodeId() const { return m_nodeId; }
    QHostAddress address() const { return m_address; }

    void setHighlighted(bool highlighted);
    bool isHighlighted() const { return m_highlighted; }

    void updateStatistics(int packetCount, quint64 bytes);

    // QGraphicsItem interface
    QRectF boundingRect() const override;
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;
    QPainterPath shape() const override;

protected:
    void hoverEnterEvent(QGraphicsSceneHoverEvent *event) override;
    void hoverLeaveEvent(QGraphicsSceneHoverEvent *event) override;
    void mousePressEvent(QGraphicsSceneMouseEvent *event) override;

private:
    QString m_nodeId;
    QHostAddress m_address;
    bool m_highlighted;
    int m_packetCount;
    quint64 m_totalBytes;
    QPropertyAnimation *m_pulseAnimation;
};

/**
 * @brief Represents a network link between two nodes
 */
class NetworkLink : public QGraphicsItem
{
public:
    explicit NetworkLink(NetworkNode *source, NetworkNode *dest, QGraphicsItem *parent = nullptr);

    NetworkNode* sourceNode() const { return m_sourceNode; }
    NetworkNode* destNode() const { return m_destNode; }

    void setHighlighted(bool highlighted);
    bool isHighlighted() const { return m_highlighted; }

    void updateTraffic(int packetCount, quint64 bytes);

    // QGraphicsItem interface
    QRectF boundingRect() const override;
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;
    QPainterPath shape() const override;

protected:
    void hoverEnterEvent(QGraphicsSceneHoverEvent *event) override;
    void hoverLeaveEvent(QGraphicsSceneHoverEvent *event) override;
    void mousePressEvent(QGraphicsSceneMouseEvent *event) override;

private:
    void updatePath();

    NetworkNode *m_sourceNode;
    NetworkNode *m_destNode;
    bool m_highlighted;
    int m_packetCount;
    quint64 m_totalBytes;
    QPainterPath m_path;
    QPropertyAnimation *m_flowAnimation;
};

/**
 * @brief Animated packet representation
 */
class PacketAnimation : public QGraphicsItem
{
public:
    explicit PacketAnimation(const PacketInfo &packet, NetworkLink *link, QGraphicsItem *parent = nullptr);

    PacketInfo packetInfo() const { return m_packet; }

    void startAnimation();
    void stopAnimation();

    // QGraphicsItem interface
    QRectF boundingRect() const override;
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;

protected:
    void mousePressEvent(QGraphicsSceneMouseEvent *event) override;

private:
    PacketInfo m_packet;
    NetworkLink *m_link;
    QPropertyAnimation *m_positionAnimation;
    QPropertyAnimation *m_opacityAnimation;
    qreal m_progress;
};

#endif // NETWORKVISUALIZATIONWIDGET_H

class NetworkVisualizationWidget : public QWidget
{
    Q_OBJECT
public:
    explicit NetworkVisualizationWidget(QWidget *parent = nullptr);
    ~NetworkVisualizationWidget();

signals:

};

#endif // NETWORKVISUALIZATIONWIDGET_H
