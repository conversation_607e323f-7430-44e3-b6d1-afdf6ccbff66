#ifndef ANIMATEDBUTTON_H
#define ANIMATEDBUTTON_H

#include <QPushButton>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>
#include <QGraphicsDropShadowEffect>
#include <QParallelAnimationGroup>
#include <QSequentialAnimationGroup>
#include <QEasingCurve>
#include <QMouseEvent>
#include <QPainter>
#include <QStyleOption>

/**
 * @brief Enhanced animated button with hover effects and smooth transitions
 *
 * This button provides sophisticated animations including:
 * - Smooth hover transitions with scale and glow effects
 * - Click animations with bounce feedback
 * - Customizable animation durations and easing curves
 * - Support for icons and rich text
 * - Accessibility features with keyboard navigation
 */
class AnimatedButton : public QPushButton
{
    Q_OBJECT
    Q_PROPERTY(qreal scale READ scale WRITE setScale)
    Q_PROPERTY(qreal glowRadius READ glowRadius WRITE setGlowRadius)
    Q_PROPERTY(qreal opacity READ opacity WRITE setOpacity)

public:
    explicit AnimatedButton(QWidget *parent = nullptr);
    explicit AnimatedButton(const QString &text, QWidget *parent = nullptr);
    explicit AnimatedButton(const QIcon &icon, const QString &text, QWidget *parent = nullptr);
    ~AnimatedButton();

    // Animation properties
    qreal scale() const { return m_scale; }
    void setScale(qreal scale);

    qreal glowRadius() const { return m_glowRadius; }
    void setGlowRadius(qreal radius);

    qreal opacity() const { return m_opacity; }
    void setOpacity(qreal opacity);

    // Animation configuration
    void setHoverAnimationDuration(int duration) { m_hoverDuration = duration; }
    void setClickAnimationDuration(int duration) { m_clickDuration = duration; }
    void setAnimationEasing(QEasingCurve::Type easing) { m_easingCurve = easing; }

    // Visual effects
    void setGlowColor(const QColor &color);
    void setHoverScale(qreal scale) { m_hoverScale = scale; }
    void setClickScale(qreal scale) { m_clickScale = scale; }

    // Enable/disable animations
    void setAnimationsEnabled(bool enabled) { m_animationsEnabled = enabled; }
    bool animationsEnabled() const { return m_animationsEnabled; }

public slots:
    void startPulseAnimation();
    void stopPulseAnimation();
    void flashSuccess();
    void flashError();

signals:
    void animationFinished();
    void hoverEntered();
    void hoverLeft();

protected:
    void enterEvent(QEnterEvent *event) override;
    void leaveEvent(QEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void paintEvent(QPaintEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

private slots:
    void onHoverAnimationFinished();
    void onClickAnimationFinished();
    void onPulseAnimationFinished();

private:
    void initializeAnimations();
    void setupEffects();
    void startHoverAnimation(bool entering);
    void startClickAnimation();
    void updateTransform();
    void updateGlowEffect();

    // Animation properties
    qreal m_scale;
    qreal m_glowRadius;
    qreal m_opacity;

    // Animation configuration
    int m_hoverDuration;
    int m_clickDuration;
    QEasingCurve::Type m_easingCurve;
    qreal m_hoverScale;
    qreal m_clickScale;
    bool m_animationsEnabled;

    // Visual effects
    QGraphicsDropShadowEffect *m_glowEffect;
    QColor m_glowColor;

    // Animation objects
    QPropertyAnimation *m_scaleAnimation;
    QPropertyAnimation *m_glowAnimation;
    QPropertyAnimation *m_opacityAnimation;
    QParallelAnimationGroup *m_hoverAnimationGroup;
    QSequentialAnimationGroup *m_clickAnimationGroup;
    QSequentialAnimationGroup *m_pulseAnimationGroup;

    // State tracking
    bool m_isHovered;
    bool m_isPressed;
    bool m_isPulsing;

    // Default values
    static const int DEFAULT_HOVER_DURATION = 200;
    static const int DEFAULT_CLICK_DURATION = 150;
    static constexpr qreal DEFAULT_HOVER_SCALE = 1.05;
    static constexpr qreal DEFAULT_CLICK_SCALE = 0.95;
};

#endif // ANIMATEDBUTTON_H
