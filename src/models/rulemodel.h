#ifndef RULEMODEL_H
#define RULEMODEL_H

#include <QAbstractListModel>
#include <QDateTime>
#include <QHostAddress>
#include <QJsonObject>
#include <QJsonDocument>
#include <QUuid>
#include "packetmodel.h"

/**
 * @brief Represents different types of firewall rule conditions
 */
enum class ConditionType {
    SOURCE_IP = 0,      ///< Source IP address condition
    DEST_IP,            ///< Destination IP address condition
    SOURCE_PORT,        ///< Source port condition
    DEST_PORT,          ///< Destination port condition
    PROTOCOL,           ///< Protocol type condition
    PACKET_SIZE,        ///< Packet size condition
    TIME_RANGE,         ///< Time-based condition
    INTERFACE,          ///< Network interface condition
    CUSTOM              ///< Custom condition with user-defined logic
};

/**
 * @brief Represents different comparison operators for rule conditions
 */
enum class ConditionOperator {
    EQUALS = 0,         ///< Exact match
    NOT_EQUALS,         ///< Not equal
    GREATER_THAN,       ///< Greater than (for numeric values)
    LESS_THAN,          ///< Less than (for numeric values)
    GREATER_EQUAL,      ///< Greater than or equal
    LESS_EQUAL,         ///< Less than or equal
    CONTAINS,           ///< Contains substring (for strings)
    STARTS_WITH,        ///< Starts with prefix
    ENDS_WITH,          ///< Ends with suffix
    REGEX_MATCH,        ///< Regular expression match
    IN_RANGE,           ///< Value within range
    IN_SUBNET           ///< IP address within subnet
};

/**
 * @brief Represents the action to take when a rule matches
 */
enum class RuleAction {
    ALLOW = 0,          ///< Allow the packet through
    BLOCK,              ///< Block the packet
    RATE_LIMIT,         ///< Apply rate limiting
    LOG_ONLY,           ///< Log the packet but don't block
    REDIRECT,           ///< Redirect to different destination
    MODIFY              ///< Modify packet headers
};

/**
 * @brief Represents a single condition within a firewall rule
 */
struct RuleCondition {
    QString id;                     ///< Unique condition identifier
    ConditionType type;             ///< Type of condition
    ConditionOperator op;           ///< Comparison operator
    QVariant value;                 ///< Primary value for comparison
    QVariant secondaryValue;        ///< Secondary value (for ranges, etc.)
    bool negated;                   ///< Whether condition is negated
    QString description;            ///< Human-readable description

    RuleCondition() : type(ConditionType::SOURCE_IP), op(ConditionOperator::EQUALS), negated(false) {
        id = QUuid::createUuid().toString(QUuid::WithoutBraces);
    }

    // Serialization
    QJsonObject toJson() const;
    static RuleCondition fromJson(const QJsonObject &json);

    // Evaluation
    bool evaluate(const PacketInfo &packet) const;
};

/**
 * @brief Represents a complete firewall rule with conditions and actions
 */
struct FirewallRule {
    QString id;                     ///< Unique rule identifier
    QString name;                   ///< Human-readable rule name
    QString description;            ///< Detailed rule description
    bool enabled;                   ///< Whether rule is active
    int priority;                   ///< Rule priority (lower = higher priority)
    QDateTime created;              ///< When rule was created
    QDateTime modified;             ///< When rule was last modified
    QString createdBy;              ///< User who created the rule

    // Rule logic
    QList<RuleCondition> conditions; ///< List of conditions (AND logic)
    RuleAction action;              ///< Action to take when rule matches
    QVariantMap actionParameters;   ///< Parameters for the action

    // Statistics
    quint64 matchCount;             ///< Number of times rule has matched
    QDateTime lastMatch;            ///< When rule last matched
    quint64 bytesProcessed;         ///< Total bytes processed by this rule

    // Rate limiting (when action is RATE_LIMIT)
    int rateLimit;                  ///< Packets per second limit
    int burstSize;                  ///< Burst size for rate limiting

    // Logging
    bool logMatches;                ///< Whether to log rule matches
    QString logLevel;               ///< Log level (DEBUG, INFO, WARN, ERROR)

    FirewallRule() : enabled(true), priority(100), action(RuleAction::ALLOW),
                     matchCount(0), bytesProcessed(0), rateLimit(0), burstSize(0),
                     logMatches(true), logLevel("INFO") {
        id = QUuid::createUuid().toString(QUuid::WithoutBraces);
        created = modified = QDateTime::currentDateTime();
    }

    // Serialization
    QJsonObject toJson() const;
    static FirewallRule fromJson(const QJsonObject &json);

    // Rule evaluation
    bool matches(const PacketInfo &packet) const;
    QString getDisplayText() const;
    QString getConditionSummary() const;
    QString getActionSummary() const;
};

/**
 * @brief Model class for managing firewall rules in Qt's Model/View framework
 *
 * This class provides a list model for displaying and managing firewall rules.
 * It supports drag-and-drop reordering, rule validation, and persistence.
 *
 * The model integrates with the rule builder UI components to provide a
 * seamless rule creation and management experience.
 */
class RuleModel : public QAbstractListModel
{
    Q_OBJECT

public:
    /**
     * @brief Custom roles for accessing rule data
     */
    enum RuleRole {
        RuleIdRole = Qt::UserRole + 1,
        RuleNameRole,
        RuleDescriptionRole,
        RuleEnabledRole,
        RulePriorityRole,
        RuleActionRole,
        RuleConditionsRole,
        RuleMatchCountRole,
        RuleLastMatchRole,
        RuleBytesProcessedRole,
        RuleCreatedRole,
        RuleModifiedRole,
        RuleCreatedByRole,
        RuleLogMatchesRole,
        RuleLogLevelRole
    };

    explicit RuleModel(QObject *parent = nullptr);
    ~RuleModel() override;

    // QAbstractListModel interface
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
    Qt::ItemFlags flags(const QModelIndex &index) const override;
    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole) override;

    // Drag and drop support
    Qt::DropActions supportedDropActions() const override;
    QStringList mimeTypes() const override;
    QMimeData *mimeData(const QModelIndexList &indexes) const override;
    bool dropMimeData(const QMimeData *data, Qt::DropAction action, int row, int column, const QModelIndex &parent) override;

    // Rule management
    void addRule(const FirewallRule &rule);
    void insertRule(int index, const FirewallRule &rule);
    void removeRule(int index);
    void removeRule(const QString &ruleId);
    void updateRule(int index, const FirewallRule &rule);
    void updateRule(const QString &ruleId, const FirewallRule &rule);
    void moveRule(int from, int to);
    void clearRules();

    // Rule access
    FirewallRule getRule(int index) const;
    FirewallRule getRule(const QString &ruleId) const;
    int getRuleIndex(const QString &ruleId) const;
    QList<FirewallRule> getAllRules() const;
    QList<FirewallRule> getEnabledRules() const;

    // Rule operations
    void enableRule(const QString &ruleId, bool enabled = true);
    void disableRule(const QString &ruleId);
    void setPriority(const QString &ruleId, int priority);
    void updateStatistics(const QString &ruleId, quint64 matchCount, quint64 bytesProcessed);

    // Validation
    bool validateRule(const FirewallRule &rule, QString &errorMessage) const;
    QStringList validateAllRules() const;

    // Persistence
    bool saveToFile(const QString &filename) const;
    bool loadFromFile(const QString &filename);
    QJsonDocument toJson() const;
    bool fromJson(const QJsonDocument &document);

    // Templates and presets
    void loadDefaultRules();
    void addTemplateRule(const QString &templateName);
    QStringList getAvailableTemplates() const;

    // Statistics
    int getTotalRules() const;
    int getEnabledRulesCount() const;
    int getDisabledRulesCount() const;
    quint64 getTotalMatches() const;
    quint64 getTotalBytesProcessed() const;

public slots:
    void refreshModel();
    void sortByPriority();
    void sortByName();
    void sortByCreated();
    void sortByMatches();

signals:
    void ruleAdded(const QString &ruleId);
    void ruleRemoved(const QString &ruleId);
    void ruleUpdated(const QString &ruleId);
    void ruleMoved(const QString &ruleId, int newIndex);
    void ruleEnabled(const QString &ruleId, bool enabled);
    void statisticsUpdated();
    void modelChanged();

private:
    void initializeTemplates();
    void sortRules(std::function<bool(const FirewallRule&, const FirewallRule&)> comparator);
    QString generateUniqueRuleName(const QString &baseName) const;

    QList<FirewallRule> m_rules;                    ///< List of all firewall rules
    QMap<QString, FirewallRule> m_ruleTemplates;    ///< Predefined rule templates
    QString m_currentFilename;                      ///< Current file for persistence
    bool m_modified;                                ///< Whether model has unsaved changes

    static const QString MIME_TYPE_RULE;
};

#endif // RULEMODEL_H