#include "alertmodel.h"
#include <QDebug>

AlertModel::AlertModel(QObject *parent)
    : QAbstractListModel(parent)
{
    qDebug() << "AlertModel initialized.";
}

AlertModel::~AlertModel()
{
    // Stub implementation - TODO: Complete implementation
}

// QAbstractListModel implementation
int AlertModel::rowCount(const QModelIndex &parent) const
{
    Q_UNUSED(parent)
    return m_alerts.size();
}

QVariant AlertModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || index.row() >= m_alerts.size())
        return QVariant();

    const AlertInfo &alert = m_alerts.at(index.row());

    switch (role) {
    case Qt::DisplayRole:
        return QString("[%1] %2").arg(alert.severityText()).arg(alert.title);
    case AlertIdRole:
        return alert.id;
    case AlertTypeRole:
        return static_cast<int>(alert.type);
    case AlertSeverityRole:
        return static_cast<int>(alert.severity);
    case AlertStatusRole:
        return static_cast<int>(alert.status);
    case AlertTimestampRole:
        return alert.timestamp;
    case AlertTitleRole:
        return alert.title;
    case AlertDescriptionRole:
        return alert.description;
    case AlertSourceRole:
        return alert.source;
    case AlertRuleIdRole:
        return alert.ruleId;
    case AlertPacketInfoRole:
        return QVariant::fromValue(alert.packetInfo);
    case AlertCountRole:
        return alert.count;
    case AlertAcknowledgedRole:
        return alert.acknowledged;
    case AlertResolvedRole:
        return alert.resolved;
    default:
        return QVariant();
    }
}

bool AlertModel::setData(const QModelIndex &index, const QVariant &value, int role)
{
    if (!index.isValid() || index.row() >= m_alerts.size())
        return false;

    AlertInfo &alert = m_alerts[index.row()];
    bool changed = false;

    switch (role) {
    case AlertAcknowledgedRole:
        if (alert.acknowledged != value.toBool()) {
            alert.acknowledged = value.toBool();
            changed = true;
        }
        break;
    case AlertResolvedRole:
        if (alert.resolved != value.toBool()) {
            alert.resolved = value.toBool();
            changed = true;
        }
        break;
    }

    if (changed) {
        emit dataChanged(index, index, {role});
        return true;
    }
    return false;
}

QVariant AlertModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation != Qt::Horizontal || role != Qt::DisplayRole)
        return QVariant();

    switch (section) {
    case 0: return tr("Alert");
    case 1: return tr("Severity");
    case 2: return tr("Time");
    case 3: return tr("Status");
    default: return QVariant();
    }
}

Qt::ItemFlags AlertModel::flags(const QModelIndex &index) const
{
    if (!index.isValid())
        return Qt::NoItemFlags;
    return Qt::ItemIsEnabled | Qt::ItemIsSelectable;
}

// Slot implementations
void AlertModel::refreshModel()
{
    beginResetModel();
    // Refresh logic would go here
    endResetModel();
}

void AlertModel::cleanupOldAlerts()
{
    beginResetModel();
    // Remove old alerts logic would go here
    endResetModel();
}

void AlertModel::aggregateSimilarAlerts()
{
    beginResetModel();
    // Aggregate similar alerts logic would go here
    endResetModel();
}
