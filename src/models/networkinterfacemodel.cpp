#include "networkinterfacemodel.h"
#include <QDebug>

NetworkInterfaceModel::NetworkInterfaceModel(QObject *parent)
    : QAbstractListModel(parent)
{
    qDebug() << "NetworkInterfaceModel initialized.";
}

NetworkInterfaceModel::~NetworkInterfaceModel()
{
    // Stub implementation - TODO: Complete implementation
}

// QAbstractListModel implementation
int NetworkInterfaceModel::rowCount(const QModelIndex &parent) const
{
    Q_UNUSED(parent)
    return m_interfaces.size();
}

QVariant NetworkInterfaceModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || index.row() >= m_interfaces.size())
        return QVariant();

    const NetworkInterfaceInfo &interface = m_interfaces.at(index.row());

    switch (role) {
    case Qt::DisplayRole:
        return QString("%1 (%2)").arg(interface.name).arg(interface.displayName);
    case NameRole:
        return interface.name;
    case DisplayNameRole:
        return interface.displayName;
    case DescriptionRole:
        return interface.description;
    case HardwareAddressRole:
        return interface.hardwareAddress;
    case AddressesRole:
        return QVariant::fromValue(interface.addresses);
    case StatusRole:
        return static_cast<int>(interface.status);
    case TypeRole:
        return static_cast<int>(interface.type);
    case IsMonitoredRole:
        return interface.isMonitored;
    case IsSelectedRole:
        return interface.isSelected;
    case StatisticsRole:
        return QVariant::fromValue(interface.statistics);
    case MTURole:
        return interface.mtu;
    case FlagsRole:
        return static_cast<int>(interface.flags);
    case LastSeenRole:
        return interface.lastSeen;
    default:
        return QVariant();
    }
}

bool NetworkInterfaceModel::setData(const QModelIndex &index, const QVariant &value, int role)
{
    if (!index.isValid() || index.row() >= m_interfaces.size())
        return false;

    NetworkInterfaceInfo &interface = m_interfaces[index.row()];
    bool changed = false;

    switch (role) {
    case IsMonitoredRole:
        if (interface.isMonitored != value.toBool()) {
            interface.isMonitored = value.toBool();
            changed = true;
        }
        break;
    case IsSelectedRole:
        if (interface.isSelected != value.toBool()) {
            interface.isSelected = value.toBool();
            changed = true;
        }
        break;
    }

    if (changed) {
        emit dataChanged(index, index, {role});
        return true;
    }
    return false;
}

QVariant NetworkInterfaceModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation != Qt::Horizontal || role != Qt::DisplayRole)
        return QVariant();

    switch (section) {
    case 0: return tr("Interface");
    case 1: return tr("Status");
    case 2: return tr("Type");
    case 3: return tr("Address");
    default: return QVariant();
    }
}

Qt::ItemFlags NetworkInterfaceModel::flags(const QModelIndex &index) const
{
    if (!index.isValid())
        return Qt::NoItemFlags;
    return Qt::ItemIsEnabled | Qt::ItemIsSelectable;
}

// Slot implementations
void NetworkInterfaceModel::startMonitoring()
{
    // Start monitoring logic would go here
}

void NetworkInterfaceModel::stopMonitoring()
{
    // Stop monitoring logic would go here
}

void NetworkInterfaceModel::refreshStatistics()
{
    beginResetModel();
    // Refresh statistics logic would go here
    endResetModel();
}

void NetworkInterfaceModel::detectNewInterfaces()
{
    beginResetModel();
    // Detect new interfaces logic would go here
    endResetModel();
}

void NetworkInterfaceModel::onRefreshTimer()
{
    // Refresh timer logic would go here
}

void NetworkInterfaceModel::onStatisticsTimer()
{
    // Statistics timer logic would go here
}
