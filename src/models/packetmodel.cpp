#include "packetmodel.h"
#include <QMutexLocker>
#include <QRandomGenerator>

PacketModel::PacketModel(QObject *parent)
    : QAbstractTableModel(parent)
    , m_filtersActive(false)
    , m_maxPackets(DEFAULT_MAX_PACKETS)
    , m_autoRefreshEnabled(true)
    , m_autoRefreshTimer(nullptr)
    , m_totalBytes(0)
{
    m_autoRefreshTimer = new QTimer(this);
    connect(m_autoRefreshTimer, &QTimer::timeout, this, &PacketModel::onAutoRefreshTimer);
    m_autoRefreshTimer->start(AUTO_REFRESH_INTERVAL);
}

PacketModel::~PacketModel() = default;

int PacketModel::rowCount(const QModelIndex &parent) const
{
    Q_UNUSED(parent)
    QMutexLocker locker(&m_mutex);
    return m_filtersActive ? m_filteredPackets.size() : m_packets.size();
}

int PacketModel::columnCount(const QModelIndex &parent) const
{
    Q_UNUSED(parent)
    return COL_COUNT;
}

QVariant PacketModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid()) return QVariant();

    QMutexLocker locker(&m_mutex);
    const QList<PacketInfo> &packets = m_filtersActive ? m_filteredPackets : m_packets;

    if (index.row() >= packets.size()) return QVariant();

    const PacketInfo &packet = packets.at(index.row());

    if (role == Qt::DisplayRole) {
        switch (index.column()) {
            case COL_ID: return packet.id;
            case COL_TIMESTAMP: return packet.timestamp.toString("hh:mm:ss.zzz");
            case COL_PROTOCOL: return protocolToString(packet.protocol);
            case COL_SOURCE_ADDRESS: return packet.sourceAddress.toString();
            case COL_DEST_ADDRESS: return packet.destinationAddress.toString();
            case COL_SOURCE_PORT: return packet.sourcePort;
            case COL_DEST_PORT: return packet.destinationPort;
            case COL_SIZE: return packet.packetSize;
            case COL_ACTION: return actionToString(packet.action);
            case COL_RULE_ID: return packet.ruleId;
            case COL_INTERFACE: return packet.interfaceName;
        }
    }

    return QVariant();
}

QVariant PacketModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation != Qt::Horizontal || role != Qt::DisplayRole) return QVariant();

    switch (section) {
        case COL_ID: return "ID";
        case COL_TIMESTAMP: return "Time";
        case COL_PROTOCOL: return "Protocol";
        case COL_SOURCE_ADDRESS: return "Source";
        case COL_DEST_ADDRESS: return "Destination";
        case COL_SOURCE_PORT: return "Src Port";
        case COL_DEST_PORT: return "Dst Port";
        case COL_SIZE: return "Size";
        case COL_ACTION: return "Action";
        case COL_RULE_ID: return "Rule";
        case COL_INTERFACE: return "Interface";
    }

    return QVariant();
}

Qt::ItemFlags PacketModel::flags(const QModelIndex &index) const
{
    Q_UNUSED(index)
    return Qt::ItemIsEnabled | Qt::ItemIsSelectable;
}

void PacketModel::addPacket(const PacketInfo &packet)
{
    QMutexLocker locker(&m_mutex);

    beginInsertRows(QModelIndex(), m_packets.size(), m_packets.size());
    m_packets.append(packet);
    m_totalBytes += packet.packetSize;

    enforceMaxPackets();

    if (passesFilters(packet)) {
        m_filteredPackets.append(packet);
    }

    endInsertRows();

    emit packetAdded(packet);
    emit statisticsChanged();
}

void PacketModel::clearPackets()
{
    QMutexLocker locker(&m_mutex);

    beginResetModel();
    m_packets.clear();
    m_filteredPackets.clear();
    m_totalBytes = 0;
    endResetModel();

    emit packetsCleared();
    emit statisticsChanged();
}

void PacketModel::setProtocolFilter(const QList<ProtocolType> &protocols)
{
    QMutexLocker locker(&m_mutex);
    m_protocolFilter = protocols;
    m_filtersActive = !protocols.isEmpty();
    applyFilters();
}

QString PacketModel::protocolToString(ProtocolType protocol) const
{
    switch (protocol) {
        case ProtocolType::IP: return "IP";
        case ProtocolType::ARP: return "ARP";
        case ProtocolType::TCP: return "TCP";
        case ProtocolType::UDP: return "UDP";
        case ProtocolType::ICMP: return "ICMP";
        default: return "Other";
    }
}

QString PacketModel::actionToString(PacketAction action) const
{
    switch (action) {
        case PacketAction::ALLOWED: return "Allowed";
        case PacketAction::BLOCKED: return "Blocked";
        case PacketAction::RATE_LIMITED: return "Rate Limited";
        case PacketAction::LOGGED_ONLY: return "Logged";
        default: return "Unknown";
    }
}

void PacketModel::applyFilters()
{
    beginResetModel();
    m_filteredPackets.clear();

    for (const PacketInfo &packet : m_packets) {
        if (passesFilters(packet)) {
            m_filteredPackets.append(packet);
        }
    }

    endResetModel();
}

bool PacketModel::passesFilters(const PacketInfo &packet) const
{
    if (!m_protocolFilter.isEmpty() && !m_protocolFilter.contains(packet.protocol)) {
        return false;
    }

    return true;
}

void PacketModel::enforceMaxPackets()
{
    while (m_packets.size() > m_maxPackets) {
        m_packets.removeFirst();
    }
}

void PacketModel::onAutoRefreshTimer()
{
    // Simulate some packet data for demonstration
    if (QRandomGenerator::global()->bounded(100) < 20) { // 20% chance
        PacketInfo packet;
        packet.id = QRandomGenerator::global()->bounded(1000000);
        packet.timestamp = QDateTime::currentDateTime();
        packet.protocol = static_cast<ProtocolType>(QRandomGenerator::global()->bounded(5));
        packet.sourceAddress = QHostAddress("192.168.1." + QString::number(QRandomGenerator::global()->bounded(255)));
        packet.destinationAddress = QHostAddress("10.0.0." + QString::number(QRandomGenerator::global()->bounded(255)));
        packet.sourcePort = QRandomGenerator::global()->bounded(65536);
        packet.destinationPort = QRandomGenerator::global()->bounded(65536);
        packet.packetSize = QRandomGenerator::global()->bounded(1500) + 64;
        packet.action = static_cast<PacketAction>(QRandomGenerator::global()->bounded(4));
        packet.interfaceName = "eth0";

        addPacket(packet);
    }
}
    : QObject(parent)
{
    qDebug() << "PacketModel initialized.";
}

PacketModel::~PacketModel()
{
    qDebug() << "PacketModel destroyed.";
}
