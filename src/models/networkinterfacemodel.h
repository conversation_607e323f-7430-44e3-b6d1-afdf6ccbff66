#ifndef NETWORKINTERFACEMODEL_H
#define NETWORKINTERFACEMODEL_H

#include <QAbstractListModel>
#include <QNetworkInterface>
#include <QHostAddress>
#include <QTimer>
#include <QMutex>
#include <QJsonObject>
#include <QJsonDocument>

/**
 * @brief Network interface status enumeration
 */
enum class InterfaceStatus {
    UNKNOWN = 0,        ///< Status unknown
    UP,                 ///< Interface is up and running
    DOWN,               ///< Interface is down
    MONITORING,         ///< Interface is being monitored
    ERROR               ///< Interface has an error
};

/**
 * @brief Network interface statistics
 */
struct InterfaceStatistics {
    quint64 bytesReceived;      ///< Total bytes received
    quint64 bytesSent;          ///< Total bytes sent
    quint64 packetsReceived;    ///< Total packets received
    quint64 packetsSent;        ///< Total packets sent
    quint64 errorsReceived;     ///< Receive errors
    quint64 errorsSent;         ///< Send errors
    quint64 droppedReceived;    ///< Dropped received packets
    quint64 droppedSent;        ///< Dropped sent packets
    QDateTime lastUpdated;      ///< Last statistics update

    InterfaceStatistics() : bytesReceived(0), bytesSent(0), packetsReceived(0),
                           packetsSent(0), errorsReceived(0), errorsSent(0),
                           droppedReceived(0), droppedSent(0) {
        lastUpdated = QDateTime::currentDateTime();
    }

    // Calculate rates (per second)
    double getReceiveRate(const InterfaceStatistics &previous) const;
    double getSendRate(const InterfaceStatistics &previous) const;
    double getPacketReceiveRate(const InterfaceStatistics &previous) const;
    double getPacketSendRate(const InterfaceStatistics &previous) const;

    // Serialization
    QJsonObject toJson() const;
    static InterfaceStatistics fromJson(const QJsonObject &json);
};

/**
 * @brief Enhanced network interface information
 */
struct NetworkInterfaceInfo {
    QString name;                           ///< Interface name (e.g., "eth0")
    QString displayName;                    ///< Human-readable name
    QString description;                    ///< Interface description
    QString hardwareAddress;                ///< MAC address
    QList<QHostAddress> addresses;          ///< IP addresses
    QList<QHostAddress> subnetMasks;        ///< Subnet masks
    QList<QHostAddress> broadcasts;         ///< Broadcast addresses
    InterfaceStatus status;                 ///< Current status
    QNetworkInterface::InterfaceFlags flags; ///< Interface flags
    int mtu;                               ///< Maximum transmission unit
    QString type;                          ///< Interface type (Ethernet, WiFi, etc.)
    bool isMonitored;                      ///< Whether this interface is monitored
    bool isSelected;                       ///< Whether this interface is selected
    InterfaceStatistics statistics;        ///< Current statistics
    InterfaceStatistics previousStatistics; ///< Previous statistics for rate calculation
    QDateTime lastSeen;                    ///< Last time interface was detected

    NetworkInterfaceInfo() : status(InterfaceStatus::UNKNOWN), mtu(0),
                            isMonitored(false), isSelected(false) {
        lastSeen = QDateTime::currentDateTime();
    }

    // Utility methods
    bool isValid() const;
    bool isUp() const;
    bool hasIPv4Address() const;
    bool hasIPv6Address() const;
    QString getPrimaryIPv4Address() const;
    QString getPrimaryIPv6Address() const;
    QString getStatusString() const;
    QString getTypeString() const;
    QColor getStatusColor() const;

    // Serialization
    QJsonObject toJson() const;
    static NetworkInterfaceInfo fromJson(const QJsonObject &json);
};

/**
 * @brief Model class for managing network interfaces in Qt's Model/View framework
 *
 * This class provides a list model for displaying and managing network interfaces.
 * It supports:
 * - Real-time interface discovery and monitoring
 * - Interface statistics tracking
 * - Interface selection for monitoring
 * - Status monitoring and change detection
 * - Export functionality for interface information
 */
class NetworkInterfaceModel : public QAbstractListModel
{
    Q_OBJECT

public:
    /**
     * @brief Custom roles for accessing interface data
     */
    enum InterfaceRole {
        NameRole = Qt::UserRole + 1,
        DisplayNameRole,
        DescriptionRole,
        HardwareAddressRole,
        AddressesRole,
        StatusRole,
        TypeRole,
        IsMonitoredRole,
        IsSelectedRole,
        StatisticsRole,
        MTURole,
        FlagsRole,
        LastSeenRole
    };

    explicit NetworkInterfaceModel(QObject *parent = nullptr);
    ~NetworkInterfaceModel();

    // QAbstractListModel interface
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
    Qt::ItemFlags flags(const QModelIndex &index) const override;
    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole) override;

    // Interface management
    void refreshInterfaces();
    void addInterface(const NetworkInterfaceInfo &interface);
    void updateInterface(const QString &name, const NetworkInterfaceInfo &interface);
    void removeInterface(const QString &name);

    // Interface access
    NetworkInterfaceInfo getInterface(int index) const;
    NetworkInterfaceInfo getInterface(const QString &name) const;
    int getInterfaceIndex(const QString &name) const;
    QList<NetworkInterfaceInfo> getAllInterfaces() const;
    QList<NetworkInterfaceInfo> getMonitoredInterfaces() const;
    QList<NetworkInterfaceInfo> getSelectedInterfaces() const;
    QStringList getInterfaceNames() const;

    // Interface selection and monitoring
    void setInterfaceMonitored(const QString &name, bool monitored);
    void setInterfaceSelected(const QString &name, bool selected);
    void setAllInterfacesMonitored(bool monitored);
    void setAllInterfacesSelected(bool selected);

    // Statistics
    void updateStatistics(const QString &name, const InterfaceStatistics &stats);
    InterfaceStatistics getStatistics(const QString &name) const;
    InterfaceStatistics getTotalStatistics() const;

    // Configuration
    void setAutoRefresh(bool enabled);
    bool isAutoRefreshEnabled() const;
    void setRefreshInterval(int milliseconds);
    int getRefreshInterval() const;

    // Persistence
    bool saveToFile(const QString &filename) const;
    bool loadFromFile(const QString &filename);
    QJsonDocument toJson() const;
    bool fromJson(const QJsonDocument &document);

public slots:
    void startMonitoring();
    void stopMonitoring();
    void refreshStatistics();
    void detectNewInterfaces();

signals:
    void interfaceAdded(const QString &name);
    void interfaceRemoved(const QString &name);
    void interfaceUpdated(const QString &name);
    void interfaceStatusChanged(const QString &name, InterfaceStatus status);
    void interfaceMonitoringChanged(const QString &name, bool monitored);
    void interfaceSelectionChanged(const QString &name, bool selected);
    void statisticsUpdated(const QString &name);
    void newInterfaceDetected(const QString &name);
    void interfaceDisconnected(const QString &name);
    void modelChanged();

private slots:
    void onRefreshTimer();
    void onStatisticsTimer();

private:
    void setupTimers();
    void updateInterfaceStatus(NetworkInterfaceInfo &interface);
    NetworkInterfaceInfo* findInterface(const QString &name);
    const NetworkInterfaceInfo* findInterface(const QString &name) const;
    void detectInterfaceChanges();
    InterfaceStatus determineStatus(const QNetworkInterface &interface);
    QString determineType(const QNetworkInterface &interface);

    QList<NetworkInterfaceInfo> m_interfaces;

    // Monitoring
    bool m_autoRefreshEnabled;
    bool m_isMonitoring;
    QTimer *m_refreshTimer;
    QTimer *m_statisticsTimer;
    int m_refreshInterval;
    int m_statisticsInterval;

    // Thread safety
    mutable QMutex m_mutex;

    // Configuration
    bool m_includeLoopback;
    bool m_includeVirtual;

    static const int DEFAULT_REFRESH_INTERVAL = 5000;      // 5 seconds
    static const int DEFAULT_STATISTICS_INTERVAL = 1000;   // 1 second
};

Q_DECLARE_METATYPE(NetworkInterfaceInfo)
Q_DECLARE_METATYPE(InterfaceStatistics)

#endif // NETWORKINTERFACEMODEL_H
