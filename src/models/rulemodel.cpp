#include "rulemodel.h"
#include <QDebug>
#include <QMimeData>
#include <QDataStream>
#include <algorithm>

RuleModel::RuleModel(QObject *parent)
    : QAbstractListModel(parent)
{
    // Stub implementation - TODO: Complete implementation
}

RuleModel::~RuleModel()
{
    // Stub implementation - TODO: Complete implementation
}

// QAbstractListModel implementation
int RuleModel::rowCount(const QModelIndex &parent) const
{
    Q_UNUSED(parent)
    return m_rules.size();
}

QVariant RuleModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || index.row() >= m_rules.size())
        return QVariant();

    const FirewallRule &rule = m_rules.at(index.row());

    switch (role) {
    case Qt::DisplayRole:
        return QString("%1 (%2)").arg(rule.name).arg(rule.enabled ? "Enabled" : "Disabled");
    case RuleIdRole:
        return rule.id;
    case RuleNameRole:
        return rule.name;
    case RuleDescriptionRole:
        return rule.description;
    case RuleEnabledRole:
        return rule.enabled;
    case RulePriorityRole:
        return rule.priority;
    case RuleActionRole:
        return static_cast<int>(rule.action);
    default:
        return QVariant();
    }
}

bool RuleModel::setData(const QModelIndex &index, const QVariant &value, int role)
{
    if (!index.isValid() || index.row() >= m_rules.size())
        return false;

    FirewallRule &rule = m_rules[index.row()];
    bool changed = false;

    switch (role) {
    case RuleNameRole:
        if (rule.name != value.toString()) {
            rule.name = value.toString();
            changed = true;
        }
        break;
    case RuleDescriptionRole:
        if (rule.description != value.toString()) {
            rule.description = value.toString();
            changed = true;
        }
        break;
    case RuleEnabledRole:
        if (rule.enabled != value.toBool()) {
            rule.enabled = value.toBool();
            changed = true;
        }
        break;
    case RulePriorityRole:
        if (rule.priority != value.toInt()) {
            rule.priority = value.toInt();
            changed = true;
        }
        break;
    }

    if (changed) {
        emit dataChanged(index, index, {role});
        return true;
    }
    return false;
}

QVariant RuleModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation != Qt::Horizontal || role != Qt::DisplayRole)
        return QVariant();

    switch (section) {
    case 0: return tr("Rule Name");
    case 1: return tr("Status");
    case 2: return tr("Priority");
    case 3: return tr("Action");
    default: return QVariant();
    }
}

Qt::ItemFlags RuleModel::flags(const QModelIndex &index) const
{
    if (!index.isValid())
        return Qt::NoItemFlags;
    return Qt::ItemIsEnabled | Qt::ItemIsSelectable | Qt::ItemIsDragEnabled;
}

// Drag and drop support
Qt::DropActions RuleModel::supportedDropActions() const
{
    return Qt::MoveAction;
}

QStringList RuleModel::mimeTypes() const
{
    QStringList types;
    types << "application/x-firewallrule";
    return types;
}

QMimeData *RuleModel::mimeData(const QModelIndexList &indexes) const
{
    QMimeData *mimeData = new QMimeData();
    QByteArray encodedData;
    QDataStream stream(&encodedData, QIODevice::WriteOnly);

    foreach (const QModelIndex &index, indexes) {
        if (index.isValid()) {
            QString text = data(index, Qt::DisplayRole).toString();
            stream << text;
        }
    }

    mimeData->setData("application/x-firewallrule", encodedData);
    return mimeData;
}

bool RuleModel::dropMimeData(const QMimeData *data, Qt::DropAction action,
                            int row, int column, const QModelIndex &parent)
{
    Q_UNUSED(column)
    Q_UNUSED(parent)

    if (action == Qt::IgnoreAction)
        return true;

    if (!data->hasFormat("application/x-firewallrule"))
        return false;

    int beginRow;
    if (row != -1)
        beginRow = row;
    else
        beginRow = rowCount(QModelIndex());

    // Simple implementation - just return true for now
    return true;
}

// Slot implementations
void RuleModel::refreshModel()
{
    beginResetModel();
    // Refresh logic would go here
    endResetModel();
}

void RuleModel::sortByPriority()
{
    beginResetModel();
    std::sort(m_rules.begin(), m_rules.end(),
              [](const FirewallRule &a, const FirewallRule &b) {
                  return a.priority > b.priority;
              });
    endResetModel();
}

void RuleModel::sortByName()
{
    beginResetModel();
    std::sort(m_rules.begin(), m_rules.end(),
              [](const FirewallRule &a, const FirewallRule &b) {
                  return a.name < b.name;
              });
    endResetModel();
}

void RuleModel::sortByCreated()
{
    beginResetModel();
    std::sort(m_rules.begin(), m_rules.end(),
              [](const FirewallRule &a, const FirewallRule &b) {
                  return a.created > b.created;
              });
    endResetModel();
}

void RuleModel::sortByMatches()
{
    beginResetModel();
    std::sort(m_rules.begin(), m_rules.end(),
              [](const FirewallRule &a, const FirewallRule &b) {
                  return a.matchCount > b.matchCount;
              });
    endResetModel();
}
