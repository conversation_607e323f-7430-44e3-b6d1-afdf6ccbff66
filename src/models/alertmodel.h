#ifndef ALERTMODEL_H
#define ALERTMODEL_H

#include <QAbstractListModel>
#include <QDateTime>
#include <QTimer>
#include <QMutex>
#include <QJsonObject>
#include <QJsonDocument>
#include "packetmodel.h"

/**
 * @brief Represents different types of security alerts
 */
enum class AlertType {
    SECURITY_BREACH = 0,    ///< Security breach detected
    SUSPICIOUS_ACTIVITY,    ///< Suspicious network activity
    RATE_LIMIT_EXCEEDED,    ///< Rate limit threshold exceeded
    RULE_VIOLATION,         ///< Firewall rule violation
    SYSTEM_ERROR,           ///< System or configuration error
    PERFORMANCE_WARNING,    ///< Performance degradation warning
    NETWORK_ANOMALY,        ///< Network behavior anomaly
    INTRUSION_ATTEMPT       ///< Intrusion attempt detected
};

/**
 * @brief Represents alert severity levels
 */
enum class AlertSeverity {
    LOW = 0,        ///< Low priority alert
    MEDIUM,         ///< Medium priority alert
    HIGH,           ///< High priority alert
    CRITICAL        ///< Critical priority alert
};

/**
 * @brief Represents alert status
 */
enum class AlertStatus {
    NEW = 0,        ///< New unread alert
    ACKNOWLEDGED,   ///< Alert has been acknowledged
    RESOLVED,       ///< Alert has been resolved
    DISMISSED       ///< Alert has been dismissed
};

/**
 * @brief Data structure representing a security alert
 */
struct AlertInfo {
    QString id;                     ///< Unique alert identifier
    AlertType type;                 ///< Type of alert
    AlertSeverity severity;         ///< Severity level
    AlertStatus status;             ///< Current status
    QDateTime timestamp;            ///< When alert was generated
    QDateTime acknowledgedTime;     ///< When alert was acknowledged
    QString title;                  ///< Alert title
    QString description;            ///< Detailed description
    QString source;                 ///< Source of the alert
    QString recommendation;         ///< Recommended action
    PacketInfo relatedPacket;       ///< Related packet (if applicable)
    PacketInfo packetInfo;          ///< Packet information (alias for compatibility)
    QString ruleId;                 ///< Related rule ID (if applicable)
    QVariantMap metadata;           ///< Additional metadata
    int count;                      ///< Number of similar alerts
    bool acknowledged;              ///< Whether alert has been acknowledged
    bool resolved;                  ///< Whether alert has been resolved

    AlertInfo() : type(AlertType::SYSTEM_ERROR), severity(AlertSeverity::LOW),
                  status(AlertStatus::NEW), count(1), acknowledged(false), resolved(false) {
        id = QUuid::createUuid().toString(QUuid::WithoutBraces);
        timestamp = QDateTime::currentDateTime();
        packetInfo = relatedPacket; // Initialize alias
    }

    // Serialization
    QJsonObject toJson() const;
    static AlertInfo fromJson(const QJsonObject &json);

    // Utility methods
    QString getTypeString() const;
    QString getSeverityString() const;
    QString severityText() const { return getSeverityString(); } // Alias for compatibility
    QString getStatusString() const;
    QColor getSeverityColor() const;
    QString getDisplayText() const;
};

/**
 * @brief Model class for managing security alerts in Qt's Model/View framework
 *
 * This class provides a list model for displaying and managing security alerts.
 * It supports real-time alert generation, filtering, acknowledgment, and persistence.
 *
 * Features:
 * - Real-time alert generation and updates
 * - Alert filtering by type, severity, and status
 * - Alert acknowledgment and resolution tracking
 * - Alert aggregation for similar events
 * - Automatic cleanup of old alerts
 * - Export functionality for alert reports
 */
class AlertModel : public QAbstractListModel
{
    Q_OBJECT

public:
    /**
     * @brief Custom roles for accessing alert data
     */
    enum AlertRole {
        AlertIdRole = Qt::UserRole + 1,
        AlertTypeRole,
        AlertSeverityRole,
        AlertStatusRole,
        AlertTimestampRole,
        AlertTitleRole,
        AlertDescriptionRole,
        AlertSourceRole,
        AlertRuleIdRole,
        AlertPacketInfoRole,
        AlertRecommendationRole,
        AlertCountRole,
        AlertAcknowledgedRole,
        AlertResolvedRole,
        AlertMetadataRole
    };

    explicit AlertModel(QObject *parent = nullptr);
    ~AlertModel();

    // QAbstractListModel interface
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
    Qt::ItemFlags flags(const QModelIndex &index) const override;
    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole) override;

    // Alert management
    void addAlert(const AlertInfo &alert);
    void updateAlert(const QString &alertId, const AlertInfo &alert);
    void removeAlert(const QString &alertId);
    void acknowledgeAlert(const QString &alertId);
    void resolveAlert(const QString &alertId);
    void dismissAlert(const QString &alertId);
    void clearAllAlerts();

    // Alert access
    AlertInfo getAlert(int index) const;
    AlertInfo getAlert(const QString &alertId) const;
    int getAlertIndex(const QString &alertId) const;
    QList<AlertInfo> getAllAlerts() const;
    QList<AlertInfo> getAlertsByType(AlertType type) const;
    QList<AlertInfo> getAlertsBySeverity(AlertSeverity severity) const;
    QList<AlertInfo> getAlertsByStatus(AlertStatus status) const;

    // Filtering
    void setTypeFilter(const QList<AlertType> &types);
    void setSeverityFilter(const QList<AlertSeverity> &severities);
    void setStatusFilter(const QList<AlertStatus> &statuses);
    void setTimeRangeFilter(const QDateTime &start, const QDateTime &end);
    void clearFilters();

    // Statistics
    int getTotalAlerts() const;
    int getNewAlertsCount() const;
    int getCriticalAlertsCount() const;
    int getAlertsCountByType(AlertType type) const;
    int getAlertsCountBySeverity(AlertSeverity severity) const;

    // Configuration
    void setMaxAlerts(int maxAlerts);
    int getMaxAlerts() const;
    void setAutoCleanup(bool enabled);
    bool isAutoCleanupEnabled() const;
    void setCleanupAge(int days);
    int getCleanupAge() const;

    // Persistence
    bool saveToFile(const QString &filename) const;
    bool loadFromFile(const QString &filename);
    QJsonDocument toJson() const;
    bool fromJson(const QJsonDocument &document);

public slots:
    void refreshModel();
    void cleanupOldAlerts();
    void aggregateSimilarAlerts();

signals:
    void alertAdded(const QString &alertId);
    void alertUpdated(const QString &alertId);
    void alertRemoved(const QString &alertId);
    void alertAcknowledged(const QString &alertId);
    void alertResolved(const QString &alertId);
    void alertDismissed(const QString &alertId);
    void newCriticalAlert(const AlertInfo &alert);
    void statisticsChanged();
    void modelChanged();

private:
    void applyFilters();
    bool passesFilters(const AlertInfo &alert) const;
    void enforceMaxAlerts();
    void updateStatistics();
    AlertInfo* findAlert(const QString &alertId);

    QList<AlertInfo> m_alerts;              ///< All alerts
    QList<AlertInfo> m_filteredAlerts;      ///< Filtered alerts for display

    // Filtering criteria
    QList<AlertType> m_typeFilter;
    QList<AlertSeverity> m_severityFilter;
    QList<AlertStatus> m_statusFilter;
    QDateTime m_timeRangeStart;
    QDateTime m_timeRangeEnd;
    bool m_filtersActive;

    // Configuration
    int m_maxAlerts;
    bool m_autoCleanupEnabled;
    int m_cleanupAgeDays;
    QTimer *m_cleanupTimer;

    // Thread safety
    mutable QMutex m_mutex;

    // Statistics cache
    int m_newAlertsCount;
    int m_criticalAlertsCount;

    static const int DEFAULT_MAX_ALERTS = 1000;
    static const int DEFAULT_CLEANUP_AGE = 30; // days
    static const int CLEANUP_INTERVAL = 3600000; // 1 hour
};

Q_DECLARE_METATYPE(AlertInfo)

#endif // ALERTMODEL_H
