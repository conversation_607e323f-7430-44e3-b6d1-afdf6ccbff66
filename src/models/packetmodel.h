#ifndef PACKETMODEL_H
#define PACKETMODEL_H

#include <QAbstractTableModel>
#include <QDateTime>
#include <QHostAddress>
#include <QList>
#include <QTimer>
#include <QMutex>

/**
 * @brief Represents different network protocol types
 *
 * This enum defines the supported protocol types for packet analysis
 * and visualization in the firewall application.
 */
enum class ProtocolType {
    IP = 0,     ///< Internet Protocol
    ARP,        ///< Address Resolution Protocol
    TCP,        ///< Transmission Control Protocol
    UDP,        ///< User Datagram Protocol
    ICMP,       ///< Internet Control Message Protocol
    OTHER       ///< Other/Unknown protocols
};

/**
 * @brief Represents the action taken on a packet
 */
enum class PacketAction {
    ALLOWED = 0,    ///< Packet was allowed through
    BLOCKED,        ///< Packet was blocked by firewall
    RATE_LIMITED,   ///< Packet was rate limited
    LOGGED_ONLY     ///< Packet was only logged (no action)
};

/**
 * @brief Data structure representing a network packet
 *
 * This structure contains all relevant information about a captured
 * network packet, including protocol details, timing, and firewall actions.
 */
struct PacketInfo {
    quint64 id;                     ///< Unique packet identifier
    QDateTime timestamp;            ///< When the packet was captured
    ProtocolType protocol;          ///< Protocol type (IP, ARP, TCP, UDP, etc.)
    QHostAddress sourceAddress;     ///< Source IP address
    QHostAddress destinationAddress; ///< Destination IP address
    quint16 sourcePort;             ///< Source port (for TCP/UDP)
    quint16 destinationPort;        ///< Destination port (for TCP/UDP)
    quint32 packetSize;             ///< Size of the packet in bytes
    PacketAction action;            ///< Action taken by firewall
    QString ruleId;                 ///< ID of the rule that matched this packet
    QString interfaceName;          ///< Network interface name
    QByteArray rawData;             ///< Raw packet data (for deep inspection)

    // Protocol-specific fields
    struct {
        quint8 version;             ///< IP version (4 or 6)
        quint8 ttl;                 ///< Time to live
        quint16 identification;     ///< IP identification field
        bool dontFragment;          ///< Don't fragment flag
        bool moreFragments;         ///< More fragments flag
    } ipInfo;

    struct {
        quint16 operation;          ///< ARP operation (request/reply)
        QByteArray senderMac;       ///< Sender MAC address
        QByteArray targetMac;       ///< Target MAC address
    } arpInfo;

    struct {
        quint32 sequenceNumber;     ///< TCP sequence number
        quint32 acknowledgmentNumber; ///< TCP acknowledgment number
        quint16 windowSize;         ///< TCP window size
        bool syn, ack, fin, rst, psh, urg; ///< TCP flags
    } tcpInfo;

    struct {
        quint16 length;             ///< UDP length field
        quint16 checksum;           ///< UDP checksum
    } udpInfo;

    // Default constructor
    PacketInfo() : id(0), protocol(ProtocolType::OTHER), sourcePort(0),
                   destinationPort(0), packetSize(0), action(PacketAction::ALLOWED) {
        // Initialize protocol-specific structs
        ipInfo = {0, 0, 0, false, false};
        arpInfo = {0, QByteArray(), QByteArray()};
        tcpInfo = {0, 0, 0, false, false, false, false, false, false};
        udpInfo = {0, 0};
    }
};

/**
 * @brief Model class for managing packet data in Qt's Model/View framework
 *
 * This class provides a table model for displaying and managing network packets
 * captured by the firewall application. It supports real-time updates, filtering,
 * and sorting capabilities.
 *
 * The model follows Qt's Model/View architecture and integrates with the
 * application's MVC design pattern for clean separation of data and presentation.
 */
class PacketModel : public QAbstractTableModel
{
    Q_OBJECT

public:
    /**
     * @brief Column indices for the packet table view
     */
    enum Column {
        COL_ID = 0,
        COL_TIMESTAMP,
        COL_PROTOCOL,
        COL_SOURCE_ADDRESS,
        COL_DEST_ADDRESS,
        COL_SOURCE_PORT,
        COL_DEST_PORT,
        COL_SIZE,
        COL_ACTION,
        COL_RULE_ID,
        COL_INTERFACE,
        COL_COUNT
    };

    explicit PacketModel(QObject *parent = nullptr);
    ~PacketModel() override;

    // QAbstractTableModel interface
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    int columnCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
    Qt::ItemFlags flags(const QModelIndex &index) const override;

    // Packet management methods
    void addPacket(const PacketInfo &packet);
    void addPackets(const QList<PacketInfo> &packets);
    void clearPackets();
    PacketInfo getPacket(int row) const;
    QList<PacketInfo> getAllPackets() const;

    // Filtering and search
    void setProtocolFilter(const QList<ProtocolType> &protocols);
    void setActionFilter(const QList<PacketAction> &actions);
    void setTimeRangeFilter(const QDateTime &start, const QDateTime &end);
    void setAddressFilter(const QString &address);
    void clearFilters();

    // Statistics
    int getPacketCount() const;
    int getPacketCountByProtocol(ProtocolType protocol) const;
    int getPacketCountByAction(PacketAction action) const;
    quint64 getTotalBytes() const;
    double getPacketsPerSecond() const;

    // Configuration
    void setMaxPackets(int maxPackets);
    int getMaxPackets() const;
    void setAutoRefresh(bool enabled);
    bool isAutoRefreshEnabled() const;

public slots:
    void refreshData();
    void startAutoRefresh();
    void stopAutoRefresh();

signals:
    void packetAdded(const PacketInfo &packet);
    void packetsCleared();
    void statisticsChanged();
    void modelUpdated();

private slots:
    void onAutoRefreshTimer();

private:
    void applyFilters();
    bool passesFilters(const PacketInfo &packet) const;
    QString protocolToString(ProtocolType protocol) const;
    QString actionToString(PacketAction action) const;
    void updateStatistics();
    void enforceMaxPackets();

    QList<PacketInfo> m_packets;           ///< All captured packets
    QList<PacketInfo> m_filteredPackets;   ///< Filtered packets for display

    // Filtering criteria
    QList<ProtocolType> m_protocolFilter;
    QList<PacketAction> m_actionFilter;
    QDateTime m_timeRangeStart;
    QDateTime m_timeRangeEnd;
    QString m_addressFilter;
    bool m_filtersActive;

    // Configuration
    int m_maxPackets;                      ///< Maximum packets to keep in memory
    bool m_autoRefreshEnabled;
    QTimer *m_autoRefreshTimer;

    // Statistics
    mutable QMutex m_mutex;                ///< Thread safety for packet access
    quint64 m_totalBytes;
    QDateTime m_firstPacketTime;
    QDateTime m_lastPacketTime;

    static const int DEFAULT_MAX_PACKETS = 10000;
    static const int AUTO_REFRESH_INTERVAL = 1000; // 1 second
};

#endif // PACKETMODEL_H