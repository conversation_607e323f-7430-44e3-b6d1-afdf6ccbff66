#ifndef CONFIGURATIONMODEL_H
#define CONFIGURATIONMODEL_H

#include <QAbstractItemModel>
#include <QSettings>
#include <QVariant>
#include <QJsonObject>
#include <QJsonDocument>
#include <QMutex>
#include <QTimer>

/**
 * @brief Configuration categories for organized settings management
 */
enum class ConfigCategory {
    APPLICATION = 0,    ///< General application settings
    NETWORK,           ///< Network monitoring settings
    FIREWALL,          ///< Firewall rule settings
    ALERTS,            ///< Alert configuration
    UI,                ///< User interface settings
    PERFORMANCE,       ///< Performance tuning settings
    LOGGING,           ///< Logging configuration
    EXPORT,            ///< Export settings
    INTEGRATION        ///< External integration settings
};

/**
 * @brief Configuration item data structure
 */
struct ConfigItem {
    QString key;                    ///< Configuration key
    QVariant value;                 ///< Current value
    QVariant defaultValue;          ///< Default value
    QString description;            ///< Human-readable description
    QString tooltip;                ///< Tooltip text
    ConfigCategory category;        ///< Category this item belongs to
    QString dataType;               ///< Data type (string, int, bool, etc.)
    QVariant minValue;              ///< Minimum value (for numeric types)
    QVariant maxValue;              ///< Maximum value (for numeric types)
    QStringList allowedValues;      ///< Allowed values (for enum types)
    bool isAdvanced;                ///< Whether this is an advanced setting
    bool requiresRestart;           ///< Whether changing this requires restart

    ConfigItem() : category(ConfigCategory::APPLICATION), isAdvanced(false), requiresRestart(false) {}

    // Validation
    bool isValid() const;
    QString getValidationError() const;

    // Serialization
    QJsonObject toJson() const;
    static ConfigItem fromJson(const QJsonObject &json);
};

/**
 * @brief Model class for managing application configuration in Qt's Model/View framework
 *
 * This class provides a hierarchical model for displaying and managing application
 * configuration settings. It supports:
 * - Categorized configuration items
 * - Real-time validation
 * - Default value restoration
 * - Import/export functionality
 * - Change tracking and persistence
 * - Advanced/basic setting modes
 */
class ConfigurationModel : public QAbstractItemModel
{
    Q_OBJECT

public:
    /**
     * @brief Custom roles for accessing configuration data
     */
    enum ConfigRole {
        KeyRole = Qt::UserRole + 1,
        ValueRole,
        DefaultValueRole,
        DescriptionRole,
        TooltipRole,
        CategoryRole,
        DataTypeRole,
        MinValueRole,
        MaxValueRole,
        AllowedValuesRole,
        IsAdvancedRole,
        RequiresRestartRole,
        IsModifiedRole
    };

    explicit ConfigurationModel(QObject *parent = nullptr);
    ~ConfigurationModel();

    // QAbstractItemModel interface
    QModelIndex index(int row, int column, const QModelIndex &parent = QModelIndex()) const override;
    QModelIndex parent(const QModelIndex &child) const override;
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    int columnCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
    Qt::ItemFlags flags(const QModelIndex &index) const override;
    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole) override;

    // Configuration management
    void addConfigItem(const ConfigItem &item);
    void removeConfigItem(const QString &key);
    void updateConfigItem(const QString &key, const ConfigItem &item);

    // Value access and modification
    QVariant getValue(const QString &key, const QVariant &defaultValue = QVariant()) const;
    void setValue(const QString &key, const QVariant &value);
    void resetToDefault(const QString &key);
    void resetCategoryToDefaults(ConfigCategory category);
    void resetAllToDefaults();

    // Configuration item access
    ConfigItem getConfigItem(const QString &key) const;
    QList<ConfigItem> getConfigItemsByCategory(ConfigCategory category) const;
    QList<ConfigItem> getAllConfigItems() const;
    QStringList getModifiedKeys() const;

    // Category management
    QStringList getCategoryNames() const;
    QString getCategoryDisplayName(ConfigCategory category) const;
    QList<ConfigCategory> getCategories() const;

    // Validation
    bool validateValue(const QString &key, const QVariant &value, QString &errorMessage) const;
    bool validateAllValues(QStringList &errors) const;

    // Advanced settings mode
    void setShowAdvancedSettings(bool show);
    bool isShowingAdvancedSettings() const;

    // Persistence
    bool loadFromSettings(QSettings *settings);
    bool saveToSettings(QSettings *settings) const;
    bool loadFromFile(const QString &filename);
    bool saveToFile(const QString &filename) const;
    QJsonDocument toJson() const;
    bool fromJson(const QJsonDocument &document);

    // Change tracking
    bool hasUnsavedChanges() const;
    void markAsSaved();
    QStringList getKeysRequiringRestart() const;

public slots:
    void refreshModel();
    void applyChanges();
    void discardChanges();
    void autoSave();

signals:
    void valueChanged(const QString &key, const QVariant &oldValue, const QVariant &newValue);
    void configItemAdded(const QString &key);
    void configItemRemoved(const QString &key);
    void configItemUpdated(const QString &key);
    void categoryChanged(ConfigCategory category);
    void unsavedChangesChanged(bool hasChanges);
    void validationError(const QString &key, const QString &error);
    void modelChanged();

private:
    void initializeDefaultConfiguration();
    void setupCategories();
    ConfigItem* findConfigItem(const QString &key);
    const ConfigItem* findConfigItem(const QString &key) const;
    QModelIndex getIndexForKey(const QString &key) const;
    void updateModifiedState();

    // Internal data structure for tree model
    struct CategoryNode {
        ConfigCategory category;
        QString displayName;
        QList<ConfigItem*> items;

        CategoryNode(ConfigCategory cat, const QString &name)
            : category(cat), displayName(name) {}
    };

    QList<CategoryNode*> m_categories;
    QMap<QString, ConfigItem*> m_configItems;
    QSet<QString> m_modifiedKeys;

    // Settings
    bool m_showAdvancedSettings;
    bool m_hasUnsavedChanges;
    bool m_autoSaveEnabled;
    QTimer *m_autoSaveTimer;

    // Thread safety
    mutable QMutex m_mutex;

    static const int AUTO_SAVE_INTERVAL = 30000; // 30 seconds
};

Q_DECLARE_METATYPE(ConfigItem)

#endif // CONFIGURATIONMODEL_H
