#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QStandardPaths>
#include <QMessageBox>
#include <QSplashScreen>
#include <QPixmap>
#include <QTimer>
#include <QCommandLineParser>
#include <QCommandLineOption>

#include "core/application.h"
#include "ui/mainwindow.h"
#include "ui/welcomewizard.h"
#include "utils/logger.h"
#include "utils/configmanager.h"
#include "utils/thememanager.h"

/**
 * @brief Sets up application directories and ensures they exist
 * @return true if successful, false otherwise
 */
bool setupApplicationDirectories()
{
    QStringList dirs = {
        QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation),
        QStandardPaths::writableLocation(QStandardPaths::AppDataLocation),
        QStandardPaths::writableLocation(QStandardPaths::CacheLocation)
    };

    for (const QString &dir : dirs) {
        QDir directory(dir);
        if (!directory.exists() && !directory.mkpath(".")) {
            qCritical() << "Failed to create directory:" << dir;
            return false;
        }
    }

    return true;
}

/**
 * @brief Checks if the application has necessary permissions for packet capture
 * @return true if permissions are adequate, false otherwise
 */
bool checkPermissions()
{
#ifdef Q_OS_LINUX
    // On Linux, check if running as root or if user is in pcap group
    if (geteuid() != 0) {
        QMessageBox::warning(nullptr, "Permission Required",
            "This application requires elevated privileges for packet capture.\n"
            "Please run as root or add your user to the 'pcap' group:\n"
            "sudo usermod -a -G pcap $USER\n\n"
            "Some features may not work without proper permissions.");
    }
#elif defined(Q_OS_WIN)
    // On Windows, check if running as administrator
    // This is a simplified check - in production, you'd use Windows APIs
    QMessageBox::information(nullptr, "Administrator Rights",
        "For full functionality, please run this application as Administrator.\n"
        "Some packet capture features may be limited without elevated privileges.");
#elif defined(Q_OS_MACOS)
    // On macOS, check for network access permissions
    QMessageBox::information(nullptr, "Network Access",
        "This application requires network access permissions.\n"
        "Please grant network access when prompted by macOS.");
#endif

    return true;
}

/**
 * @brief Shows a splash screen during application startup
 * @param app The QApplication instance
 * @return Pointer to the splash screen
 */
QSplashScreen* showSplashScreen(QApplication &app)
{
    // Create a simple splash screen
    QPixmap splashPixmap(400, 300);
    splashPixmap.fill(QColor(45, 45, 48)); // Dark background

    QSplashScreen *splash = new QSplashScreen(splashPixmap);
    splash->setStyleSheet(
        "QSplashScreen {"
        "    color: white;"
        "    font-size: 14px;"
        "    font-weight: bold;"
        "}"
    );

    splash->show();
    splash->showMessage("Loading QT Firewall...", Qt::AlignCenter | Qt::AlignBottom, Qt::white);
    app.processEvents();

    return splash;
}

/**
 * @brief Main application entry point
 *
 * This function initializes the Qt application, sets up logging, configuration,
 * and launches either the welcome wizard (for first-time users) or the main
 * application window.
 *
 * The application follows these startup phases:
 * 1. Command line parsing and basic setup
 * 2. Directory creation and permission checks
 * 3. Logging and configuration initialization
 * 4. Theme and style setup
 * 5. Splash screen display
 * 6. Core application initialization
 * 7. UI launch (wizard or main window)
 */
int main(int argc, char *argv[])
{
    // Enable high DPI support
    QApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    QApplication::setAttribute(Qt::AA_UseHighDpiPixmaps);

    QApplication app(argc, argv);

    // Set application metadata
    app.setApplicationName("QT Firewall");
    app.setApplicationVersion("1.0.0");
    app.setApplicationDisplayName("QT Firewall");
    app.setOrganizationName("QT Firewall Team");
    app.setOrganizationDomain("qtfirewall.org");

    // Parse command line arguments
    QCommandLineParser parser;
    parser.setApplicationDescription("Advanced Qt-based Firewall Application with Real-time Monitoring");
    parser.addHelpOption();
    parser.addVersionOption();

    QCommandLineOption configOption(QStringList() << "c" << "config",
        "Use custom configuration file", "config-file");
    parser.addOption(configOption);

    QCommandLineOption debugOption(QStringList() << "d" << "debug",
        "Enable debug logging");
    parser.addOption(debugOption);

    QCommandLineOption noWizardOption("no-wizard",
        "Skip welcome wizard and go directly to main window");
    parser.addOption(noWizardOption);

    parser.process(app);

    // Setup application directories
    if (!setupApplicationDirectories()) {
        QMessageBox::critical(nullptr, "Startup Error",
            "Failed to create application directories. Please check permissions.");
        return 1;
    }

    // Initialize logging
    Logger::initialize(parser.isSet(debugOption) ? Logger::DEBUG : Logger::INFO);
    qInfo() << "Starting QT Firewall application version" << app.applicationVersion();

    // Check permissions
    if (!checkPermissions()) {
        qWarning() << "Permission check failed, some features may not work";
    }

    // Initialize configuration manager
    QString configFile = parser.value(configOption);
    if (!ConfigManager::instance().initialize(configFile)) {
        QMessageBox::critical(nullptr, "Configuration Error",
            "Failed to initialize configuration. Using defaults.");
    }

    // Initialize theme manager and apply theme
    ThemeManager::instance().initialize();
    ThemeManager::instance().applyTheme(
        ConfigManager::instance().getValue("ui/theme", "dark").toString()
    );

    // Show splash screen
    QSplashScreen *splash = showSplashScreen(app);

    // Initialize core application
    splash->showMessage("Initializing core components...", Qt::AlignCenter | Qt::AlignBottom, Qt::white);
    app.processEvents();

    Application coreApp;
    if (!coreApp.initialize()) {
        splash->close();
        delete splash;
        QMessageBox::critical(nullptr, "Initialization Error",
            "Failed to initialize core application components.");
        return 1;
    }

    // Determine whether to show wizard or main window
    bool showWizard = !parser.isSet(noWizardOption) &&
                      ConfigManager::instance().getValue("app/first_run", true).toBool();

    QWidget *mainWidget = nullptr;

    if (showWizard) {
        splash->showMessage("Launching welcome wizard...", Qt::AlignCenter | Qt::AlignBottom, Qt::white);
        app.processEvents();

        WelcomeWizard *wizard = new WelcomeWizard();
        mainWidget = wizard;

        // Connect wizard completion to main window launch
        QObject::connect(wizard, &WelcomeWizard::finished, [&](int result) {
            if (result == QDialog::Accepted) {
                // Mark first run as complete
                ConfigManager::instance().setValue("app/first_run", false);

                // Launch main window
                MainWindow *mainWindow = new MainWindow();
                mainWindow->show();
                wizard->deleteLater();
            } else {
                // User cancelled wizard, exit application
                app.quit();
            }
        });
    } else {
        splash->showMessage("Launching main application...", Qt::AlignCenter | Qt::AlignBottom, Qt::white);
        app.processEvents();

        MainWindow *mainWindow = new MainWindow();
        mainWidget = mainWindow;
    }

    // Hide splash screen and show main widget
    QTimer::singleShot(2000, [splash, mainWidget]() {
        splash->close();
        splash->deleteLater();
        if (mainWidget) {
            mainWidget->show();
        }
    });

    qInfo() << "Application startup completed successfully";

    // Run the application event loop
    int result = app.exec();

    // Cleanup
    qInfo() << "Application shutting down";
    Logger::cleanup();

    return result;
}