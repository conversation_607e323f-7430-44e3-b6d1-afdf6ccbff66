# QT Firewall Application

A sophisticated Qt C++ firewall application with real-time packet monitoring, animated visualization, and advanced rule management capabilities.

## Features

- **Real-time Packet Monitoring**: Monitor IP, ARP, TCP, and UDP traffic across network interfaces
- **Animated Network Visualization**: Interactive network topology with live packet flow animations
- **Advanced Rule Management**: Drag-and-drop rule creation with visual condition builder
- **Alert System**: Real-time alerts with drill-down capabilities and diagnostics
- **Comprehensive Reporting**: Export functionality for PDF, CSV, JSON, and PowerPoint formats
- **Configuration Wizard**: Guided setup with animated onboarding experience
- **Theme Management**: Light/Dark mode with smooth transitions
- **Integration Hub**: Support for syslog, SNMP, and SIEM connectors

## Architecture

The application follows a Model-View-Controller (MVC) architecture with clear separation of concerns:

- **Presentation Layer**: Qt Widgets-based UI with custom animated components
- **Business Logic Layer**: Application controllers and service classes
- **Data Layer**: Qt Model/View framework for data management
- **Network Layer**: Packet capture and protocol analysis engines

## Requirements

### System Requirements
- Qt 6.0 or later
- CMake 3.16 or later
- C++17 compatible compiler
- Network packet capture capabilities (libpcap on Unix/macOS, WinPcap/Npcap on Windows)

### Qt Components
- Qt Core
- Qt Widgets
- Qt Network
- Qt Charts
- Qt SVG
- Qt Concurrent
- Qt PrintSupport

### Platform-specific Dependencies

#### Linux
```bash
sudo apt-get install libpcap-dev  # Ubuntu/Debian
sudo yum install libpcap-devel    # CentOS/RHEL
```

#### macOS
```bash
brew install libpcap
```

#### Windows
- Install Npcap or WinPcap
- Visual Studio 2019 or later (for MSVC compiler)

## Building the Application

### Using CMake (Recommended)

1. **Clone and navigate to the project directory**:
   ```bash
   cd QT_Firewall
   ```

2. **Create build directory**:
   ```bash
   mkdir build
   cd build
   ```

3. **Configure the project**:
   ```bash
   cmake ..
   ```

4. **Build the application**:
   ```bash
   cmake --build .
   ```

5. **Run the application**:
   ```bash
   ./QTFirewall  # Linux/macOS
   QTFirewall.exe  # Windows
   ```
