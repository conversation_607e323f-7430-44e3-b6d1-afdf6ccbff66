/* Dark Theme for QT Firewall Application */

QMainWindow {
    background-color: #2d2d30;
    color: #ffffff;
}

QMenuBar {
    background-color: #3c3c3c;
    color: #ffffff;
    border-bottom: 1px solid #555555;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background-color: #007acc;
}

QToolBar {
    background-color: #3c3c3c;
    border: none;
    spacing: 3px;
}

QToolButton {
    background-color: transparent;
    border: none;
    padding: 8px;
    border-radius: 4px;
}

QToolButton:hover {
    background-color: #007acc;
}

QStatusBar {
    background-color: #007acc;
    color: #ffffff;
}

QWidget {
    background-color: #2d2d30;
    color: #ffffff;
}

QPushButton {
    background-color: #0e639c;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    color: #ffffff;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #1177bb;
}

QPushButton:pressed {
    background-color: #005a9e;
}

QTableView {
    background-color: #252526;
    alternate-background-color: #2d2d30;
    gridline-color: #3e3e42;
    selection-background-color: #007acc;
}

QHeaderView::section {
    background-color: #3c3c3c;
    color: #ffffff;
    padding: 8px;
    border: none;
    border-right: 1px solid #555555;
}

QScrollBar:vertical {
    background-color: #3e3e42;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #686868;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9e9e9e;
}

QLineEdit {
    background-color: #3c3c3c;
    border: 1px solid #555555;
    padding: 6px;
    border-radius: 4px;
    color: #ffffff;
}

QLineEdit:focus {
    border-color: #007acc;
}
