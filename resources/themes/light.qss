/* Light Theme for QT Firewall Application */

QMainWindow {
    background-color: #ffffff;
    color: #000000;
}

QMenuBar {
    background-color: #f0f0f0;
    color: #000000;
    border-bottom: 1px solid #cccccc;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background-color: #0078d4;
    color: #ffffff;
}

QToolBar {
    background-color: #f0f0f0;
    border: none;
    spacing: 3px;
}

QToolButton {
    background-color: transparent;
    border: none;
    padding: 8px;
    border-radius: 4px;
}

QToolButton:hover {
    background-color: #0078d4;
    color: #ffffff;
}

QStatusBar {
    background-color: #0078d4;
    color: #ffffff;
}

QWidget {
    background-color: #ffffff;
    color: #000000;
}

QPushButton {
    background-color: #0078d4;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    color: #ffffff;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #106ebe;
}

QPushButton:pressed {
    background-color: #005a9e;
}

QTableView {
    background-color: #ffffff;
    alternate-background-color: #f8f8f8;
    gridline-color: #e0e0e0;
    selection-background-color: #0078d4;
    selection-color: #ffffff;
}

QHeaderView::section {
    background-color: #f0f0f0;
    color: #000000;
    padding: 8px;
    border: none;
    border-right: 1px solid #cccccc;
}

QLineEdit {
    background-color: #ffffff;
    border: 1px solid #cccccc;
    padding: 6px;
    border-radius: 4px;
    color: #000000;
}

QLineEdit:focus {
    border-color: #0078d4;
}
