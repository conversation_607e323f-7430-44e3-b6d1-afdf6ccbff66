{"cmake": {"generator": {"multiConfig": false, "name": "Unix Makefiles"}, "paths": {"cmake": "/usr/bin/cmake", "cpack": "/usr/bin/cpack", "ctest": "/usr/bin/ctest", "root": "/usr/share/cmake-3.25"}, "version": {"isDirty": false, "major": 3, "minor": 25, "patch": 1, "string": "3.25.1", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-560424d3ad30ce66791b.json", "kind": "codemodel", "version": {"major": 2, "minor": 4}}, {"jsonFile": "cache-v2-904eb8de732de134baa2.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-f1ee22dbd6da56f20e3e.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-bb86b4a9f4804aa81012.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-904eb8de732de134baa2.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-560424d3ad30ce66791b.json", "kind": "codemodel", "version": {"major": 2, "minor": 4}}, {"jsonFile": "toolchains-v1-bb86b4a9f4804aa81012.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-f1ee22dbd6da56f20e3e.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}]}}}}