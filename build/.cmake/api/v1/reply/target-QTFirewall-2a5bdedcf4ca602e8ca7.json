{"artifacts": [{"path": "QTFirewall"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "set_property", "_qt_internal_find_third_party_dependencies", "target_compile_options", "include_directories"], "files": ["CMakeLists.txt", "/usr/lib/x86_64-linux-gnu/cmake/Qt6OpenGLWidgets/Qt6OpenGLWidgetsVersionlessTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6OpenGLWidgets/Qt6OpenGLWidgetsConfig.cmake", "/usr/share/cmake-3.25/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicDependencyHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Charts/Qt6ChartsDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Charts/Qt6ChartsConfig.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6Config.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6OpenGL/Qt6OpenGLVersionlessTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6OpenGL/Qt6OpenGLConfig.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "/usr/share/cmake-3.25/Modules/FindOpenGL.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/FindWrapOpenGL.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Gui/Qt6GuiDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Gui/Qt6GuiConfig.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 141, "parent": 0}, {"command": 1, "file": 0, "line": 189, "parent": 0}, {"command": 2, "file": 0, "line": 162, "parent": 0}, {"command": 2, "file": 0, "line": 176, "parent": 0}, {"command": 5, "file": 0, "line": 9, "parent": 0}, {"file": 7, "parent": 5}, {"command": 5, "file": 7, "line": 167, "parent": 6}, {"file": 6, "parent": 7}, {"command": 4, "file": 6, "line": 50, "parent": 8}, {"file": 5, "parent": 9}, {"command": 7, "file": 5, "line": 39, "parent": 10}, {"command": 6, "file": 4, "line": 108, "parent": 11}, {"command": 5, "file": 3, "line": 47, "parent": 12}, {"file": 2, "parent": 13}, {"command": 4, "file": 2, "line": 65, "parent": 14}, {"file": 1, "parent": 15}, {"command": 3, "file": 1, "line": 71, "parent": 16}, {"command": 6, "file": 4, "line": 108, "parent": 11}, {"command": 5, "file": 3, "line": 47, "parent": 18}, {"file": 9, "parent": 19}, {"command": 4, "file": 9, "line": 65, "parent": 20}, {"file": 8, "parent": 21}, {"command": 3, "file": 8, "line": 71, "parent": 22}, {"command": 5, "file": 7, "line": 167, "parent": 6}, {"file": 11, "parent": 24}, {"command": 4, "file": 11, "line": 62, "parent": 25}, {"file": 10, "parent": 26}, {"command": 3, "file": 10, "line": 71, "parent": 27}, {"command": 4, "file": 11, "line": 50, "parent": 25}, {"file": 16, "parent": 29}, {"command": 7, "file": 16, "line": 39, "parent": 30}, {"command": 6, "file": 4, "line": 108, "parent": 31}, {"command": 5, "file": 3, "line": 47, "parent": 32}, {"file": 15, "parent": 33}, {"command": 4, "file": 15, "line": 50, "parent": 34}, {"file": 14, "parent": 35}, {"command": 9, "file": 14, "line": 30, "parent": 36}, {"command": 6, "file": 4, "line": 33, "parent": 37}, {"command": 5, "file": 3, "line": 47, "parent": 38}, {"file": 13, "parent": 39}, {"command": 5, "file": 13, "line": 10, "parent": 40}, {"file": 12, "parent": 41}, {"command": 8, "file": 12, "line": 512, "parent": 42}, {"command": 3, "file": 12, "line": 510, "parent": 42}, {"command": 10, "file": 0, "line": 185, "parent": 0}, {"command": 11, "file": 0, "line": 149, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"backtrace": 45, "fragment": "-Wall"}, {"backtrace": 45, "fragment": "-Wextra"}, {"backtrace": 45, "fragment": "-Wpedantic"}, {"backtrace": 3, "fragment": "-fPIC"}, {"fragment": "-std=gnu++17"}], "defines": [{"backtrace": 3, "define": "QT_CHARTS_LIB"}, {"backtrace": 3, "define": "QT_CONCURRENT_LIB"}, {"backtrace": 3, "define": "QT_CORE_LIB"}, {"backtrace": 3, "define": "QT_GUI_LIB"}, {"backtrace": 3, "define": "QT_NETWORK_LIB"}, {"backtrace": 3, "define": "QT_NO_DEBUG"}, {"backtrace": 3, "define": "QT_OPENGLWIDGETS_LIB"}, {"backtrace": 3, "define": "QT_OPENGL_LIB"}, {"backtrace": 3, "define": "QT_PRINTSUPPORT_LIB"}, {"backtrace": 3, "define": "QT_SVG_LIB"}, {"backtrace": 3, "define": "QT_WIDGETS_LIB"}], "includes": [{"backtrace": 0, "path": "/home/<USER>/Desktop/dev/QT_Firewall/build/QTFirewall_autogen/include"}, {"backtrace": 46, "path": "/home/<USER>/Desktop/dev/QT_Firewall/src"}, {"backtrace": 46, "path": "/home/<USER>/Desktop/dev/QT_Firewall/src/core"}, {"backtrace": 46, "path": "/home/<USER>/Desktop/dev/QT_Firewall/src/models"}, {"backtrace": 46, "path": "/home/<USER>/Desktop/dev/QT_Firewall/src/network"}, {"backtrace": 46, "path": "/home/<USER>/Desktop/dev/QT_Firewall/src/ui"}, {"backtrace": 46, "path": "/home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets"}, {"backtrace": 46, "path": "/home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs"}, {"backtrace": 46, "path": "/home/<USER>/Desktop/dev/QT_Firewall/src/ui/views"}, {"backtrace": 46, "path": "/home/<USER>/Desktop/dev/QT_Firewall/src/utils"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt6/QtCore"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt6"}, {"backtrace": 3, "isSystem": true, "path": "/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt6/QtWidgets"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt6/QtGui"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt6/QtNetwork"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt6/QtCharts"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt6/QtOpenGL"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt6/QtOpenGLWidgets"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt6/QtSvg"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt6/QtConcurrent"}, {"backtrace": 3, "isSystem": true, "path": "/usr/include/x86_64-linux-gnu/qt6/QtPrintSupport"}], "language": "CXX", "languageStandard": {"backtraces": [3, 3], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 77]}], "dependencies": [{"backtrace": 0, "id": "QTFirewall_autogen::@6890427a1f51a3e7e1df"}], "id": "QTFirewall::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "/usr/local"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"backtrace": 3, "fragment": "/usr/lib/x86_64-linux-gnu/libQt6Network.so.6.4.2", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/lib/x86_64-linux-gnu/libQt6Charts.so.6.4.2", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/lib/x86_64-linux-gnu/libQt6Svg.so.6.4.2", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/lib/x86_64-linux-gnu/libQt6Concurrent.so.6.4.2", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/lib/x86_64-linux-gnu/libQt6PrintSupport.so.6.4.2", "role": "libraries"}, {"backtrace": 4, "fragment": "-lpcap", "role": "libraries"}, {"backtrace": 17, "fragment": "/usr/lib/x86_64-linux-gnu/libQt6OpenGLWidgets.so.6.4.2", "role": "libraries"}, {"backtrace": 23, "fragment": "/usr/lib/x86_64-linux-gnu/libQt6OpenGL.so.6.4.2", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/lib/x86_64-linux-gnu/libQt6Widgets.so.6.4.2", "role": "libraries"}, {"backtrace": 28, "fragment": "/usr/lib/x86_64-linux-gnu/libQt6Gui.so.6.4.2", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/lib/x86_64-linux-gnu/libQt6Core.so.6.4.2", "role": "libraries"}, {"backtrace": 43, "fragment": "/usr/lib/x86_64-linux-gnu/libGLX.so", "role": "libraries"}, {"backtrace": 44, "fragment": "/usr/lib/x86_64-linux-gnu/libOpenGL.so", "role": "libraries"}], "language": "CXX"}, "name": "QTFirewall", "nameOnDisk": "QTFirewall", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 77]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75]}, {"name": "", "sourceIndexes": [76]}, {"name": "CMake Rules", "sourceIndexes": [78]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/QTFirewall_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/core/application.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/core/applicationcontroller.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/models/packetmodel.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/models/rulemodel.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/models/alertmodel.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/models/networkinterfacemodel.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/models/configurationmodel.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/network/packetcapture.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/network/protocolanalyzer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/network/ipanalyzer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/network/arpanalyzer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/network/tcpanalyzer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/network/udpanalyzer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/network/firewallengine.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ui/mainwindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ui/welcomewizard.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ui/dashboardview.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ui/rulemanagerview.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ui/alertview.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ui/reportview.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ui/settingsview.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ui/widgets/networkvisualizationwidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ui/widgets/packetflowwidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ui/widgets/rulebuilderwidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ui/widgets/alertpanelwidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ui/widgets/statisticswidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ui/widgets/animatedbutton.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ui/widgets/protocolfilterwidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ui/dialogs/ruledialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ui/dialogs/settingsdialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ui/dialogs/exportdialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ui/dialogs/aboutdialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/utils/logger.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/utils/configmanager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/utils/exportmanager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/utils/animationhelper.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/utils/thememanager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/core/application.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/core/applicationcontroller.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/models/packetmodel.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/models/rulemodel.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/models/alertmodel.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/models/networkinterfacemodel.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/models/configurationmodel.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/network/packetcapture.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/network/protocolanalyzer.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/network/ipanalyzer.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/network/arpanalyzer.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/network/tcpanalyzer.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/network/udpanalyzer.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/network/firewallengine.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/ui/mainwindow.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/ui/welcomewizard.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/ui/dashboardview.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/ui/rulemanagerview.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/ui/alertview.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/ui/reportview.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/ui/settingsview.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/ui/widgets/networkvisualizationwidget.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/ui/widgets/packetflowwidget.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/ui/widgets/rulebuilderwidget.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/ui/widgets/alertpanelwidget.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/ui/widgets/statisticswidget.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/ui/widgets/animatedbutton.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/ui/widgets/protocolfilterwidget.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/ui/dialogs/ruledialog.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/ui/dialogs/settingsdialog.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/ui/dialogs/exportdialog.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/ui/dialogs/aboutdialog.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/utils/logger.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/utils/configmanager.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/utils/exportmanager.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/utils/animationhelper.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/utils/thememanager.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "resources/resources.qrc", "sourceGroupIndex": 2}, {"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp.rule", "sourceGroupIndex": 3}], "type": "EXECUTABLE"}