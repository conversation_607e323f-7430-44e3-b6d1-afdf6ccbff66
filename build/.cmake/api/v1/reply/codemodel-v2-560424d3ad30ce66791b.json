{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-eb850a6a196bae57ffc1.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "QTFirewall", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "QTFirewall::@6890427a1f51a3e7e1df", "jsonFile": "target-QTFirewall-2a5bdedcf4ca602e8ca7.json", "name": "QTFirewall", "projectIndex": 0}, {"directoryIndex": 0, "id": "QTFirewall_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-QTFirewall_autogen-151e5008ae2218bb3b3f.json", "name": "QTFirewall_autogen", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/Desktop/dev/QT_Firewall/build", "source": "/home/<USER>/Desktop/dev/QT_Firewall"}, "version": {"major": 2, "minor": 4}}