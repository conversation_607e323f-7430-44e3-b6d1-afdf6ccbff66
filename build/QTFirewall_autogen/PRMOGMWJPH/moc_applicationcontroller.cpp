/****************************************************************************
** Meta object code from reading C++ file 'applicationcontroller.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.4.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/core/applicationcontroller.h"
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'applicationcontroller.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.4.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
namespace {
struct qt_meta_stringdata_ApplicationController_t {
    uint offsetsAndSizes[102];
    char stringdata0[22];
    char stringdata1[18];
    char stringdata2[1];
    char stringdata3[18];
    char stringdata4[17];
    char stringdata5[18];
    char stringdata6[20];
    char stringdata7[19];
    char stringdata8[21];
    char stringdata9[15];
    char stringdata10[10];
    char stringdata11[6];
    char stringdata12[18];
    char stringdata13[8];
    char stringdata14[18];
    char stringdata15[23];
    char stringdata16[14];
    char stringdata17[6];
    char stringdata18[14];
    char stringdata19[7];
    char stringdata20[16];
    char stringdata21[15];
    char stringdata22[16];
    char stringdata23[17];
    char stringdata24[18];
    char stringdata25[18];
    char stringdata26[19];
    char stringdata27[16];
    char stringdata28[13];
    char stringdata29[5];
    char stringdata30[19];
    char stringdata31[7];
    char stringdata32[19];
    char stringdata33[19];
    char stringdata34[8];
    char stringdata35[17];
    char stringdata36[15];
    char stringdata37[13];
    char stringdata38[9];
    char stringdata39[7];
    char stringdata40[17];
    char stringdata41[15];
    char stringdata42[15];
    char stringdata43[17];
    char stringdata44[11];
    char stringdata45[7];
    char stringdata46[14];
    char stringdata47[26];
    char stringdata48[23];
    char stringdata49[18];
    char stringdata50[13];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_ApplicationController_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_ApplicationController_t qt_meta_stringdata_ApplicationController = {
    {
        QT_MOC_LITERAL(0, 21),  // "ApplicationController"
        QT_MOC_LITERAL(22, 17),  // "monitoringStarted"
        QT_MOC_LITERAL(40, 0),  // ""
        QT_MOC_LITERAL(41, 17),  // "monitoringStopped"
        QT_MOC_LITERAL(59, 16),  // "monitoringPaused"
        QT_MOC_LITERAL(76, 17),  // "monitoringResumed"
        QT_MOC_LITERAL(94, 19),  // "configurationLoaded"
        QT_MOC_LITERAL(114, 18),  // "configurationSaved"
        QT_MOC_LITERAL(133, 20),  // "configurationChanged"
        QT_MOC_LITERAL(154, 14),  // "alertGenerated"
        QT_MOC_LITERAL(169, 9),  // "AlertInfo"
        QT_MOC_LITERAL(179, 5),  // "alert"
        QT_MOC_LITERAL(185, 17),  // "alertAcknowledged"
        QT_MOC_LITERAL(203, 7),  // "alertId"
        QT_MOC_LITERAL(211, 17),  // "statisticsUpdated"
        QT_MOC_LITERAL(229, 22),  // "networkTopologyChanged"
        QT_MOC_LITERAL(252, 13),  // "errorOccurred"
        QT_MOC_LITERAL(266, 5),  // "error"
        QT_MOC_LITERAL(272, 13),  // "statusChanged"
        QT_MOC_LITERAL(286, 6),  // "status"
        QT_MOC_LITERAL(293, 15),  // "startMonitoring"
        QT_MOC_LITERAL(309, 14),  // "stopMonitoring"
        QT_MOC_LITERAL(324, 15),  // "pauseMonitoring"
        QT_MOC_LITERAL(340, 16),  // "resumeMonitoring"
        QT_MOC_LITERAL(357, 17),  // "loadConfiguration"
        QT_MOC_LITERAL(375, 17),  // "saveConfiguration"
        QT_MOC_LITERAL(393, 18),  // "resetConfiguration"
        QT_MOC_LITERAL(412, 15),  // "addFirewallRule"
        QT_MOC_LITERAL(428, 12),  // "FirewallRule"
        QT_MOC_LITERAL(441, 4),  // "rule"
        QT_MOC_LITERAL(446, 18),  // "updateFirewallRule"
        QT_MOC_LITERAL(465, 6),  // "ruleId"
        QT_MOC_LITERAL(472, 18),  // "removeFirewallRule"
        QT_MOC_LITERAL(491, 18),  // "enableFirewallRule"
        QT_MOC_LITERAL(510, 7),  // "enabled"
        QT_MOC_LITERAL(518, 16),  // "acknowledgeAlert"
        QT_MOC_LITERAL(535, 14),  // "clearAllAlerts"
        QT_MOC_LITERAL(550, 12),  // "exportAlerts"
        QT_MOC_LITERAL(563, 8),  // "filename"
        QT_MOC_LITERAL(572, 6),  // "format"
        QT_MOC_LITERAL(579, 16),  // "exportPacketData"
        QT_MOC_LITERAL(596, 14),  // "exportRuleData"
        QT_MOC_LITERAL(611, 14),  // "generateReport"
        QT_MOC_LITERAL(626, 16),  // "onPacketCaptured"
        QT_MOC_LITERAL(643, 10),  // "PacketInfo"
        QT_MOC_LITERAL(654, 6),  // "packet"
        QT_MOC_LITERAL(661, 13),  // "onRuleMatched"
        QT_MOC_LITERAL(675, 25),  // "onNetworkInterfaceChanged"
        QT_MOC_LITERAL(701, 22),  // "onConfigurationChanged"
        QT_MOC_LITERAL(724, 17),  // "onStatisticsTimer"
        QT_MOC_LITERAL(742, 12)   // "onAlertTimer"
    },
    "ApplicationController",
    "monitoringStarted",
    "",
    "monitoringStopped",
    "monitoringPaused",
    "monitoringResumed",
    "configurationLoaded",
    "configurationSaved",
    "configurationChanged",
    "alertGenerated",
    "AlertInfo",
    "alert",
    "alertAcknowledged",
    "alertId",
    "statisticsUpdated",
    "networkTopologyChanged",
    "errorOccurred",
    "error",
    "statusChanged",
    "status",
    "startMonitoring",
    "stopMonitoring",
    "pauseMonitoring",
    "resumeMonitoring",
    "loadConfiguration",
    "saveConfiguration",
    "resetConfiguration",
    "addFirewallRule",
    "FirewallRule",
    "rule",
    "updateFirewallRule",
    "ruleId",
    "removeFirewallRule",
    "enableFirewallRule",
    "enabled",
    "acknowledgeAlert",
    "clearAllAlerts",
    "exportAlerts",
    "filename",
    "format",
    "exportPacketData",
    "exportRuleData",
    "generateReport",
    "onPacketCaptured",
    "PacketInfo",
    "packet",
    "onRuleMatched",
    "onNetworkInterfaceChanged",
    "onConfigurationChanged",
    "onStatisticsTimer",
    "onAlertTimer"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_ApplicationController[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
      37,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
      13,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,  236,    2, 0x06,    1 /* Public */,
       3,    0,  237,    2, 0x06,    2 /* Public */,
       4,    0,  238,    2, 0x06,    3 /* Public */,
       5,    0,  239,    2, 0x06,    4 /* Public */,
       6,    0,  240,    2, 0x06,    5 /* Public */,
       7,    0,  241,    2, 0x06,    6 /* Public */,
       8,    0,  242,    2, 0x06,    7 /* Public */,
       9,    1,  243,    2, 0x06,    8 /* Public */,
      12,    1,  246,    2, 0x06,   10 /* Public */,
      14,    0,  249,    2, 0x06,   12 /* Public */,
      15,    0,  250,    2, 0x06,   13 /* Public */,
      16,    1,  251,    2, 0x06,   14 /* Public */,
      18,    1,  254,    2, 0x06,   16 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      20,    0,  257,    2, 0x0a,   18 /* Public */,
      21,    0,  258,    2, 0x0a,   19 /* Public */,
      22,    0,  259,    2, 0x0a,   20 /* Public */,
      23,    0,  260,    2, 0x0a,   21 /* Public */,
      24,    0,  261,    2, 0x0a,   22 /* Public */,
      25,    0,  262,    2, 0x0a,   23 /* Public */,
      26,    0,  263,    2, 0x0a,   24 /* Public */,
      27,    1,  264,    2, 0x0a,   25 /* Public */,
      30,    2,  267,    2, 0x0a,   27 /* Public */,
      32,    1,  272,    2, 0x0a,   30 /* Public */,
      33,    2,  275,    2, 0x0a,   32 /* Public */,
      33,    1,  280,    2, 0x2a,   35 /* Public | MethodCloned */,
      35,    1,  283,    2, 0x0a,   37 /* Public */,
      36,    0,  286,    2, 0x0a,   39 /* Public */,
      37,    2,  287,    2, 0x0a,   40 /* Public */,
      40,    2,  292,    2, 0x0a,   43 /* Public */,
      41,    2,  297,    2, 0x0a,   46 /* Public */,
      42,    2,  302,    2, 0x0a,   49 /* Public */,
      43,    1,  307,    2, 0x08,   52 /* Private */,
      46,    2,  310,    2, 0x08,   54 /* Private */,
      47,    0,  315,    2, 0x08,   57 /* Private */,
      48,    0,  316,    2, 0x08,   58 /* Private */,
      49,    0,  317,    2, 0x08,   59 /* Private */,
      50,    0,  318,    2, 0x08,   60 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 10,   11,
    QMetaType::Void, QMetaType::QString,   13,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   17,
    QMetaType::Void, QMetaType::QString,   19,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 28,   29,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 28,   31,   29,
    QMetaType::Void, QMetaType::QString,   31,
    QMetaType::Void, QMetaType::QString, QMetaType::Bool,   31,   34,
    QMetaType::Void, QMetaType::QString,   31,
    QMetaType::Void, QMetaType::QString,   13,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   38,   39,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   38,   39,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   38,   39,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   38,   39,
    QMetaType::Void, 0x80000000 | 44,   45,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 44,   31,   45,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject ApplicationController::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_ApplicationController.offsetsAndSizes,
    qt_meta_data_ApplicationController,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_ApplicationController_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<ApplicationController, std::true_type>,
        // method 'monitoringStarted'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'monitoringStopped'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'monitoringPaused'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'monitoringResumed'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'configurationLoaded'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'configurationSaved'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'configurationChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'alertGenerated'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const AlertInfo &, std::false_type>,
        // method 'alertAcknowledged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'statisticsUpdated'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'networkTopologyChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'errorOccurred'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'statusChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'startMonitoring'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'stopMonitoring'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'pauseMonitoring'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'resumeMonitoring'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'loadConfiguration'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'saveConfiguration'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'resetConfiguration'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'addFirewallRule'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const FirewallRule &, std::false_type>,
        // method 'updateFirewallRule'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const FirewallRule &, std::false_type>,
        // method 'removeFirewallRule'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'enableFirewallRule'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'enableFirewallRule'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'acknowledgeAlert'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'clearAllAlerts'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'exportAlerts'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'exportPacketData'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'exportRuleData'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'generateReport'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'onPacketCaptured'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const PacketInfo &, std::false_type>,
        // method 'onRuleMatched'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const PacketInfo &, std::false_type>,
        // method 'onNetworkInterfaceChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onConfigurationChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onStatisticsTimer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onAlertTimer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void ApplicationController::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ApplicationController *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->monitoringStarted(); break;
        case 1: _t->monitoringStopped(); break;
        case 2: _t->monitoringPaused(); break;
        case 3: _t->monitoringResumed(); break;
        case 4: _t->configurationLoaded(); break;
        case 5: _t->configurationSaved(); break;
        case 6: _t->configurationChanged(); break;
        case 7: _t->alertGenerated((*reinterpret_cast< std::add_pointer_t<AlertInfo>>(_a[1]))); break;
        case 8: _t->alertAcknowledged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 9: _t->statisticsUpdated(); break;
        case 10: _t->networkTopologyChanged(); break;
        case 11: _t->errorOccurred((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 12: _t->statusChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 13: _t->startMonitoring(); break;
        case 14: _t->stopMonitoring(); break;
        case 15: _t->pauseMonitoring(); break;
        case 16: _t->resumeMonitoring(); break;
        case 17: _t->loadConfiguration(); break;
        case 18: _t->saveConfiguration(); break;
        case 19: _t->resetConfiguration(); break;
        case 20: _t->addFirewallRule((*reinterpret_cast< std::add_pointer_t<FirewallRule>>(_a[1]))); break;
        case 21: _t->updateFirewallRule((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<FirewallRule>>(_a[2]))); break;
        case 22: _t->removeFirewallRule((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 23: _t->enableFirewallRule((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 24: _t->enableFirewallRule((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 25: _t->acknowledgeAlert((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 26: _t->clearAllAlerts(); break;
        case 27: _t->exportAlerts((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 28: _t->exportPacketData((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 29: _t->exportRuleData((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 30: _t->generateReport((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 31: _t->onPacketCaptured((*reinterpret_cast< std::add_pointer_t<PacketInfo>>(_a[1]))); break;
        case 32: _t->onRuleMatched((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<PacketInfo>>(_a[2]))); break;
        case 33: _t->onNetworkInterfaceChanged(); break;
        case 34: _t->onConfigurationChanged(); break;
        case 35: _t->onStatisticsTimer(); break;
        case 36: _t->onAlertTimer(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
        case 7:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< AlertInfo >(); break;
            }
            break;
        case 31:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< PacketInfo >(); break;
            }
            break;
        case 32:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 1:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< PacketInfo >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ApplicationController::*)();
            if (_t _q_method = &ApplicationController::monitoringStarted; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ApplicationController::*)();
            if (_t _q_method = &ApplicationController::monitoringStopped; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ApplicationController::*)();
            if (_t _q_method = &ApplicationController::monitoringPaused; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ApplicationController::*)();
            if (_t _q_method = &ApplicationController::monitoringResumed; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (ApplicationController::*)();
            if (_t _q_method = &ApplicationController::configurationLoaded; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (ApplicationController::*)();
            if (_t _q_method = &ApplicationController::configurationSaved; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (ApplicationController::*)();
            if (_t _q_method = &ApplicationController::configurationChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (ApplicationController::*)(const AlertInfo & );
            if (_t _q_method = &ApplicationController::alertGenerated; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 7;
                return;
            }
        }
        {
            using _t = void (ApplicationController::*)(const QString & );
            if (_t _q_method = &ApplicationController::alertAcknowledged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 8;
                return;
            }
        }
        {
            using _t = void (ApplicationController::*)();
            if (_t _q_method = &ApplicationController::statisticsUpdated; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 9;
                return;
            }
        }
        {
            using _t = void (ApplicationController::*)();
            if (_t _q_method = &ApplicationController::networkTopologyChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 10;
                return;
            }
        }
        {
            using _t = void (ApplicationController::*)(const QString & );
            if (_t _q_method = &ApplicationController::errorOccurred; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 11;
                return;
            }
        }
        {
            using _t = void (ApplicationController::*)(const QString & );
            if (_t _q_method = &ApplicationController::statusChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 12;
                return;
            }
        }
    }
}

const QMetaObject *ApplicationController::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ApplicationController::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ApplicationController.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ApplicationController::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 37)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 37;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 37)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 37;
    }
    return _id;
}

// SIGNAL 0
void ApplicationController::monitoringStarted()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ApplicationController::monitoringStopped()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ApplicationController::monitoringPaused()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ApplicationController::monitoringResumed()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ApplicationController::configurationLoaded()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ApplicationController::configurationSaved()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void ApplicationController::configurationChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void ApplicationController::alertGenerated(const AlertInfo & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 7, _a);
}

// SIGNAL 8
void ApplicationController::alertAcknowledged(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 8, _a);
}

// SIGNAL 9
void ApplicationController::statisticsUpdated()
{
    QMetaObject::activate(this, &staticMetaObject, 9, nullptr);
}

// SIGNAL 10
void ApplicationController::networkTopologyChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 10, nullptr);
}

// SIGNAL 11
void ApplicationController::errorOccurred(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 11, _a);
}

// SIGNAL 12
void ApplicationController::statusChanged(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 12, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
