/****************************************************************************
** Meta object code from reading C++ file 'rulebuilderwidget.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.4.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/ui/widgets/rulebuilderwidget.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'rulebuilderwidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.4.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
namespace {
struct qt_meta_stringdata_RuleBuilderWidget_t {
    uint offsetsAndSizes[82];
    char stringdata0[18];
    char stringdata1[12];
    char stringdata2[1];
    char stringdata3[14];
    char stringdata4[8];
    char stringdata5[6];
    char stringdata6[15];
    char stringdata7[12];
    char stringdata8[17];
    char stringdata9[12];
    char stringdata10[9];
    char stringdata11[14];
    char stringdata12[18];
    char stringdata13[11];
    char stringdata14[22];
    char stringdata15[12];
    char stringdata16[18];
    char stringdata17[13];
    char stringdata18[5];
    char stringdata19[13];
    char stringdata20[10];
    char stringdata21[16];
    char stringdata22[13];
    char stringdata23[19];
    char stringdata24[16];
    char stringdata25[13];
    char stringdata26[13];
    char stringdata27[9];
    char stringdata28[12];
    char stringdata29[16];
    char stringdata30[19];
    char stringdata31[16];
    char stringdata32[25];
    char stringdata33[18];
    char stringdata34[17];
    char stringdata35[17];
    char stringdata36[6];
    char stringdata37[12];
    char stringdata38[12];
    char stringdata39[23];
    char stringdata40[20];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_RuleBuilderWidget_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_RuleBuilderWidget_t qt_meta_stringdata_RuleBuilderWidget = {
    {
        QT_MOC_LITERAL(0, 17),  // "RuleBuilderWidget"
        QT_MOC_LITERAL(18, 11),  // "ruleChanged"
        QT_MOC_LITERAL(30, 0),  // ""
        QT_MOC_LITERAL(31, 13),  // "ruleValidated"
        QT_MOC_LITERAL(45, 7),  // "isValid"
        QT_MOC_LITERAL(53, 5),  // "error"
        QT_MOC_LITERAL(59, 14),  // "conditionAdded"
        QT_MOC_LITERAL(74, 11),  // "conditionId"
        QT_MOC_LITERAL(86, 16),  // "conditionRemoved"
        QT_MOC_LITERAL(103, 11),  // "actionAdded"
        QT_MOC_LITERAL(115, 8),  // "actionId"
        QT_MOC_LITERAL(124, 13),  // "actionRemoved"
        QT_MOC_LITERAL(138, 17),  // "complexityChanged"
        QT_MOC_LITERAL(156, 10),  // "complexity"
        QT_MOC_LITERAL(167, 21),  // "optimizationSuggested"
        QT_MOC_LITERAL(189, 11),  // "suggestions"
        QT_MOC_LITERAL(201, 17),  // "ruleTestRequested"
        QT_MOC_LITERAL(219, 12),  // "FirewallRule"
        QT_MOC_LITERAL(232, 4),  // "rule"
        QT_MOC_LITERAL(237, 12),  // "addCondition"
        QT_MOC_LITERAL(250, 9),  // "addAction"
        QT_MOC_LITERAL(260, 15),  // "removeCondition"
        QT_MOC_LITERAL(276, 12),  // "removeAction"
        QT_MOC_LITERAL(289, 18),  // "clearAllConditions"
        QT_MOC_LITERAL(308, 15),  // "clearAllActions"
        QT_MOC_LITERAL(324, 12),  // "validateRule"
        QT_MOC_LITERAL(337, 12),  // "optimizeRule"
        QT_MOC_LITERAL(350, 8),  // "testRule"
        QT_MOC_LITERAL(359, 11),  // "previewRule"
        QT_MOC_LITERAL(371, 15),  // "resetToDefaults"
        QT_MOC_LITERAL(387, 18),  // "onConditionChanged"
        QT_MOC_LITERAL(406, 15),  // "onActionChanged"
        QT_MOC_LITERAL(422, 24),  // "onLogicalOperatorChanged"
        QT_MOC_LITERAL(447, 17),  // "onValidationTimer"
        QT_MOC_LITERAL(465, 16),  // "onDragEnterEvent"
        QT_MOC_LITERAL(482, 16),  // "QDragEnterEvent*"
        QT_MOC_LITERAL(499, 5),  // "event"
        QT_MOC_LITERAL(505, 11),  // "onDropEvent"
        QT_MOC_LITERAL(517, 11),  // "QDropEvent*"
        QT_MOC_LITERAL(529, 22),  // "onConditionDragStarted"
        QT_MOC_LITERAL(552, 19)   // "onActionDragStarted"
    },
    "RuleBuilderWidget",
    "ruleChanged",
    "",
    "ruleValidated",
    "isValid",
    "error",
    "conditionAdded",
    "conditionId",
    "conditionRemoved",
    "actionAdded",
    "actionId",
    "actionRemoved",
    "complexityChanged",
    "complexity",
    "optimizationSuggested",
    "suggestions",
    "ruleTestRequested",
    "FirewallRule",
    "rule",
    "addCondition",
    "addAction",
    "removeCondition",
    "removeAction",
    "clearAllConditions",
    "clearAllActions",
    "validateRule",
    "optimizeRule",
    "testRule",
    "previewRule",
    "resetToDefaults",
    "onConditionChanged",
    "onActionChanged",
    "onLogicalOperatorChanged",
    "onValidationTimer",
    "onDragEnterEvent",
    "QDragEnterEvent*",
    "event",
    "onDropEvent",
    "QDropEvent*",
    "onConditionDragStarted",
    "onActionDragStarted"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_RuleBuilderWidget[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
      28,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       9,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,  182,    2, 0x06,    1 /* Public */,
       3,    2,  183,    2, 0x06,    2 /* Public */,
       6,    1,  188,    2, 0x06,    5 /* Public */,
       8,    1,  191,    2, 0x06,    7 /* Public */,
       9,    1,  194,    2, 0x06,    9 /* Public */,
      11,    1,  197,    2, 0x06,   11 /* Public */,
      12,    1,  200,    2, 0x06,   13 /* Public */,
      14,    1,  203,    2, 0x06,   15 /* Public */,
      16,    1,  206,    2, 0x06,   17 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      19,    0,  209,    2, 0x0a,   19 /* Public */,
      20,    0,  210,    2, 0x0a,   20 /* Public */,
      21,    1,  211,    2, 0x0a,   21 /* Public */,
      22,    1,  214,    2, 0x0a,   23 /* Public */,
      23,    0,  217,    2, 0x0a,   25 /* Public */,
      24,    0,  218,    2, 0x0a,   26 /* Public */,
      25,    0,  219,    2, 0x0a,   27 /* Public */,
      26,    0,  220,    2, 0x0a,   28 /* Public */,
      27,    0,  221,    2, 0x0a,   29 /* Public */,
      28,    0,  222,    2, 0x0a,   30 /* Public */,
      29,    0,  223,    2, 0x0a,   31 /* Public */,
      30,    0,  224,    2, 0x08,   32 /* Private */,
      31,    0,  225,    2, 0x08,   33 /* Private */,
      32,    0,  226,    2, 0x08,   34 /* Private */,
      33,    0,  227,    2, 0x08,   35 /* Private */,
      34,    1,  228,    2, 0x08,   36 /* Private */,
      37,    1,  231,    2, 0x08,   38 /* Private */,
      39,    0,  234,    2, 0x08,   40 /* Private */,
      40,    0,  235,    2, 0x08,   41 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool, QMetaType::QString,    4,    5,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, QMetaType::QString,   10,
    QMetaType::Void, QMetaType::QString,   10,
    QMetaType::Void, QMetaType::Int,   13,
    QMetaType::Void, QMetaType::QStringList,   15,
    QMetaType::Void, 0x80000000 | 17,   18,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, QMetaType::QString,   10,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 35,   36,
    QMetaType::Void, 0x80000000 | 38,   36,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject RuleBuilderWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_RuleBuilderWidget.offsetsAndSizes,
    qt_meta_data_RuleBuilderWidget,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_RuleBuilderWidget_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<RuleBuilderWidget, std::true_type>,
        // method 'ruleChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'ruleValidated'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'conditionAdded'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'conditionRemoved'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'actionAdded'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'actionRemoved'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'complexityChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'optimizationSuggested'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QStringList &, std::false_type>,
        // method 'ruleTestRequested'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const FirewallRule &, std::false_type>,
        // method 'addCondition'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'addAction'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'removeCondition'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'removeAction'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'clearAllConditions'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'clearAllActions'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'validateRule'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'optimizeRule'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'testRule'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'previewRule'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'resetToDefaults'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onConditionChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onActionChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onLogicalOperatorChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onValidationTimer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onDragEnterEvent'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QDragEnterEvent *, std::false_type>,
        // method 'onDropEvent'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QDropEvent *, std::false_type>,
        // method 'onConditionDragStarted'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onActionDragStarted'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void RuleBuilderWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<RuleBuilderWidget *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->ruleChanged(); break;
        case 1: _t->ruleValidated((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 2: _t->conditionAdded((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->conditionRemoved((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 4: _t->actionAdded((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 5: _t->actionRemoved((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 6: _t->complexityChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 7: _t->optimizationSuggested((*reinterpret_cast< std::add_pointer_t<QStringList>>(_a[1]))); break;
        case 8: _t->ruleTestRequested((*reinterpret_cast< std::add_pointer_t<FirewallRule>>(_a[1]))); break;
        case 9: _t->addCondition(); break;
        case 10: _t->addAction(); break;
        case 11: _t->removeCondition((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 12: _t->removeAction((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 13: _t->clearAllConditions(); break;
        case 14: _t->clearAllActions(); break;
        case 15: _t->validateRule(); break;
        case 16: _t->optimizeRule(); break;
        case 17: _t->testRule(); break;
        case 18: _t->previewRule(); break;
        case 19: _t->resetToDefaults(); break;
        case 20: _t->onConditionChanged(); break;
        case 21: _t->onActionChanged(); break;
        case 22: _t->onLogicalOperatorChanged(); break;
        case 23: _t->onValidationTimer(); break;
        case 24: _t->onDragEnterEvent((*reinterpret_cast< std::add_pointer_t<QDragEnterEvent*>>(_a[1]))); break;
        case 25: _t->onDropEvent((*reinterpret_cast< std::add_pointer_t<QDropEvent*>>(_a[1]))); break;
        case 26: _t->onConditionDragStarted(); break;
        case 27: _t->onActionDragStarted(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (RuleBuilderWidget::*)();
            if (_t _q_method = &RuleBuilderWidget::ruleChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (RuleBuilderWidget::*)(bool , const QString & );
            if (_t _q_method = &RuleBuilderWidget::ruleValidated; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (RuleBuilderWidget::*)(const QString & );
            if (_t _q_method = &RuleBuilderWidget::conditionAdded; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (RuleBuilderWidget::*)(const QString & );
            if (_t _q_method = &RuleBuilderWidget::conditionRemoved; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (RuleBuilderWidget::*)(const QString & );
            if (_t _q_method = &RuleBuilderWidget::actionAdded; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (RuleBuilderWidget::*)(const QString & );
            if (_t _q_method = &RuleBuilderWidget::actionRemoved; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (RuleBuilderWidget::*)(int );
            if (_t _q_method = &RuleBuilderWidget::complexityChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (RuleBuilderWidget::*)(const QStringList & );
            if (_t _q_method = &RuleBuilderWidget::optimizationSuggested; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 7;
                return;
            }
        }
        {
            using _t = void (RuleBuilderWidget::*)(const FirewallRule & );
            if (_t _q_method = &RuleBuilderWidget::ruleTestRequested; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 8;
                return;
            }
        }
    }
}

const QMetaObject *RuleBuilderWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *RuleBuilderWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_RuleBuilderWidget.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int RuleBuilderWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 28)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 28;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 28)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 28;
    }
    return _id;
}

// SIGNAL 0
void RuleBuilderWidget::ruleChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void RuleBuilderWidget::ruleValidated(bool _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void RuleBuilderWidget::conditionAdded(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void RuleBuilderWidget::conditionRemoved(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void RuleBuilderWidget::actionAdded(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void RuleBuilderWidget::actionRemoved(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void RuleBuilderWidget::complexityChanged(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void RuleBuilderWidget::optimizationSuggested(const QStringList & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 7, _a);
}

// SIGNAL 8
void RuleBuilderWidget::ruleTestRequested(const FirewallRule & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 8, _a);
}
namespace {
struct qt_meta_stringdata_RuleConditionWidget_t {
    uint offsetsAndSizes[16];
    char stringdata0[20];
    char stringdata1[17];
    char stringdata2[1];
    char stringdata3[16];
    char stringdata4[15];
    char stringdata5[18];
    char stringdata6[15];
    char stringdata7[16];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_RuleConditionWidget_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_RuleConditionWidget_t qt_meta_stringdata_RuleConditionWidget = {
    {
        QT_MOC_LITERAL(0, 19),  // "RuleConditionWidget"
        QT_MOC_LITERAL(20, 16),  // "conditionChanged"
        QT_MOC_LITERAL(37, 0),  // ""
        QT_MOC_LITERAL(38, 15),  // "removeRequested"
        QT_MOC_LITERAL(54, 14),  // "onFieldChanged"
        QT_MOC_LITERAL(69, 17),  // "onOperatorChanged"
        QT_MOC_LITERAL(87, 14),  // "onValueChanged"
        QT_MOC_LITERAL(102, 15)   // "onRemoveClicked"
    },
    "RuleConditionWidget",
    "conditionChanged",
    "",
    "removeRequested",
    "onFieldChanged",
    "onOperatorChanged",
    "onValueChanged",
    "onRemoveClicked"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_RuleConditionWidget[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
       6,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,   50,    2, 0x06,    1 /* Public */,
       3,    0,   51,    2, 0x06,    2 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       4,    0,   52,    2, 0x08,    3 /* Private */,
       5,    0,   53,    2, 0x08,    4 /* Private */,
       6,    0,   54,    2, 0x08,    5 /* Private */,
       7,    0,   55,    2, 0x08,    6 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject RuleConditionWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_RuleConditionWidget.offsetsAndSizes,
    qt_meta_data_RuleConditionWidget,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_RuleConditionWidget_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<RuleConditionWidget, std::true_type>,
        // method 'conditionChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'removeRequested'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onFieldChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onOperatorChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onValueChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onRemoveClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void RuleConditionWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<RuleConditionWidget *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->conditionChanged(); break;
        case 1: _t->removeRequested(); break;
        case 2: _t->onFieldChanged(); break;
        case 3: _t->onOperatorChanged(); break;
        case 4: _t->onValueChanged(); break;
        case 5: _t->onRemoveClicked(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (RuleConditionWidget::*)();
            if (_t _q_method = &RuleConditionWidget::conditionChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (RuleConditionWidget::*)();
            if (_t _q_method = &RuleConditionWidget::removeRequested; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
    }
    (void)_a;
}

const QMetaObject *RuleConditionWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *RuleConditionWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_RuleConditionWidget.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int RuleConditionWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 6)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 6;
    }
    return _id;
}

// SIGNAL 0
void RuleConditionWidget::conditionChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void RuleConditionWidget::removeRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}
namespace {
struct qt_meta_stringdata_RuleActionWidget_t {
    uint offsetsAndSizes[14];
    char stringdata0[17];
    char stringdata1[14];
    char stringdata2[1];
    char stringdata3[16];
    char stringdata4[14];
    char stringdata5[20];
    char stringdata6[16];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_RuleActionWidget_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_RuleActionWidget_t qt_meta_stringdata_RuleActionWidget = {
    {
        QT_MOC_LITERAL(0, 16),  // "RuleActionWidget"
        QT_MOC_LITERAL(17, 13),  // "actionChanged"
        QT_MOC_LITERAL(31, 0),  // ""
        QT_MOC_LITERAL(32, 15),  // "removeRequested"
        QT_MOC_LITERAL(48, 13),  // "onTypeChanged"
        QT_MOC_LITERAL(62, 19),  // "onParametersChanged"
        QT_MOC_LITERAL(82, 15)   // "onRemoveClicked"
    },
    "RuleActionWidget",
    "actionChanged",
    "",
    "removeRequested",
    "onTypeChanged",
    "onParametersChanged",
    "onRemoveClicked"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_RuleActionWidget[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
       5,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,   44,    2, 0x06,    1 /* Public */,
       3,    0,   45,    2, 0x06,    2 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       4,    0,   46,    2, 0x08,    3 /* Private */,
       5,    0,   47,    2, 0x08,    4 /* Private */,
       6,    0,   48,    2, 0x08,    5 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject RuleActionWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_RuleActionWidget.offsetsAndSizes,
    qt_meta_data_RuleActionWidget,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_RuleActionWidget_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<RuleActionWidget, std::true_type>,
        // method 'actionChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'removeRequested'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onTypeChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onParametersChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onRemoveClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void RuleActionWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<RuleActionWidget *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->actionChanged(); break;
        case 1: _t->removeRequested(); break;
        case 2: _t->onTypeChanged(); break;
        case 3: _t->onParametersChanged(); break;
        case 4: _t->onRemoveClicked(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (RuleActionWidget::*)();
            if (_t _q_method = &RuleActionWidget::actionChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (RuleActionWidget::*)();
            if (_t _q_method = &RuleActionWidget::removeRequested; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
    }
    (void)_a;
}

const QMetaObject *RuleActionWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *RuleActionWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_RuleActionWidget.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int RuleActionWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 5)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 5;
    }
    return _id;
}

// SIGNAL 0
void RuleActionWidget::actionChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void RuleActionWidget::removeRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
