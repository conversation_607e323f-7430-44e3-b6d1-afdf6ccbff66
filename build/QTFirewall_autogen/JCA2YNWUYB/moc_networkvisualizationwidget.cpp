/****************************************************************************
** Meta object code from reading C++ file 'networkvisualizationwidget.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.4.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/ui/widgets/networkvisualizationwidget.h"
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'networkvisualizationwidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.4.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
namespace {
struct qt_meta_stringdata_NetworkVisualizationWidget_t {
    uint offsetsAndSizes[54];
    char stringdata0[27];
    char stringdata1[12];
    char stringdata2[1];
    char stringdata3[7];
    char stringdata4[12];
    char stringdata5[7];
    char stringdata6[14];
    char stringdata7[11];
    char stringdata8[7];
    char stringdata9[12];
    char stringdata10[7];
    char stringdata11[10];
    char stringdata12[13];
    char stringdata13[22];
    char stringdata14[10];
    char stringdata15[7];
    char stringdata16[8];
    char stringdata17[10];
    char stringdata18[15];
    char stringdata19[14];
    char stringdata20[15];
    char stringdata21[17];
    char stringdata22[14];
    char stringdata23[8];
    char stringdata24[14];
    char stringdata25[26];
    char stringdata26[11];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_NetworkVisualizationWidget_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_NetworkVisualizationWidget_t qt_meta_stringdata_NetworkVisualizationWidget = {
    {
        QT_MOC_LITERAL(0, 26),  // "NetworkVisualizationWidget"
        QT_MOC_LITERAL(27, 11),  // "nodeClicked"
        QT_MOC_LITERAL(39, 0),  // ""
        QT_MOC_LITERAL(40, 6),  // "nodeId"
        QT_MOC_LITERAL(47, 11),  // "linkClicked"
        QT_MOC_LITERAL(59, 6),  // "linkId"
        QT_MOC_LITERAL(66, 13),  // "packetClicked"
        QT_MOC_LITERAL(80, 10),  // "PacketInfo"
        QT_MOC_LITERAL(91, 6),  // "packet"
        QT_MOC_LITERAL(98, 11),  // "zoomChanged"
        QT_MOC_LITERAL(110, 6),  // "factor"
        QT_MOC_LITERAL(117, 9),  // "addPacket"
        QT_MOC_LITERAL(127, 12),  // "clearPackets"
        QT_MOC_LITERAL(140, 21),  // "updateNetworkTopology"
        QT_MOC_LITERAL(162, 9),  // "resetView"
        QT_MOC_LITERAL(172, 6),  // "zoomIn"
        QT_MOC_LITERAL(179, 7),  // "zoomOut"
        QT_MOC_LITERAL(187, 9),  // "zoomToFit"
        QT_MOC_LITERAL(197, 14),  // "startAnimation"
        QT_MOC_LITERAL(212, 13),  // "stopAnimation"
        QT_MOC_LITERAL(226, 14),  // "pauseAnimation"
        QT_MOC_LITERAL(241, 16),  // "onAnimationTimer"
        QT_MOC_LITERAL(258, 13),  // "onNodeHovered"
        QT_MOC_LITERAL(272, 7),  // "hovered"
        QT_MOC_LITERAL(280, 13),  // "onLinkHovered"
        QT_MOC_LITERAL(294, 25),  // "onPacketAnimationFinished"
        QT_MOC_LITERAL(320, 10)   // "zoomFactor"
    },
    "NetworkVisualizationWidget",
    "nodeClicked",
    "",
    "nodeId",
    "linkClicked",
    "linkId",
    "packetClicked",
    "PacketInfo",
    "packet",
    "zoomChanged",
    "factor",
    "addPacket",
    "clearPackets",
    "updateNetworkTopology",
    "resetView",
    "zoomIn",
    "zoomOut",
    "zoomToFit",
    "startAnimation",
    "stopAnimation",
    "pauseAnimation",
    "onAnimationTimer",
    "onNodeHovered",
    "hovered",
    "onLinkHovered",
    "onPacketAnimationFinished",
    "zoomFactor"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_NetworkVisualizationWidget[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
      18,   14, // methods
       1,  154, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    1,  122,    2, 0x06,    2 /* Public */,
       4,    1,  125,    2, 0x06,    4 /* Public */,
       6,    1,  128,    2, 0x06,    6 /* Public */,
       9,    1,  131,    2, 0x06,    8 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      11,    1,  134,    2, 0x0a,   10 /* Public */,
      12,    0,  137,    2, 0x0a,   12 /* Public */,
      13,    0,  138,    2, 0x0a,   13 /* Public */,
      14,    0,  139,    2, 0x0a,   14 /* Public */,
      15,    0,  140,    2, 0x0a,   15 /* Public */,
      16,    0,  141,    2, 0x0a,   16 /* Public */,
      17,    0,  142,    2, 0x0a,   17 /* Public */,
      18,    0,  143,    2, 0x0a,   18 /* Public */,
      19,    0,  144,    2, 0x0a,   19 /* Public */,
      20,    0,  145,    2, 0x0a,   20 /* Public */,
      21,    0,  146,    2, 0x08,   21 /* Private */,
      22,    1,  147,    2, 0x08,   22 /* Private */,
      24,    1,  150,    2, 0x08,   24 /* Private */,
      25,    0,  153,    2, 0x08,   26 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void, 0x80000000 | 7,    8,
    QMetaType::Void, QMetaType::QReal,   10,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 7,    8,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,   23,
    QMetaType::Void, QMetaType::Bool,   23,
    QMetaType::Void,

 // properties: name, type, flags
      26, QMetaType::QReal, 0x00015103, uint(-1), 0,

       0        // eod
};

Q_CONSTINIT const QMetaObject NetworkVisualizationWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QGraphicsView::staticMetaObject>(),
    qt_meta_stringdata_NetworkVisualizationWidget.offsetsAndSizes,
    qt_meta_data_NetworkVisualizationWidget,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_NetworkVisualizationWidget_t,
        // property 'zoomFactor'
        QtPrivate::TypeAndForceComplete<qreal, std::true_type>,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<NetworkVisualizationWidget, std::true_type>,
        // method 'nodeClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'linkClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'packetClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const PacketInfo &, std::false_type>,
        // method 'zoomChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<qreal, std::false_type>,
        // method 'addPacket'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const PacketInfo &, std::false_type>,
        // method 'clearPackets'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'updateNetworkTopology'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'resetView'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'zoomIn'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'zoomOut'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'zoomToFit'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'startAnimation'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'stopAnimation'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'pauseAnimation'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onAnimationTimer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onNodeHovered'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'onLinkHovered'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'onPacketAnimationFinished'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void NetworkVisualizationWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<NetworkVisualizationWidget *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->nodeClicked((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 1: _t->linkClicked((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 2: _t->packetClicked((*reinterpret_cast< std::add_pointer_t<PacketInfo>>(_a[1]))); break;
        case 3: _t->zoomChanged((*reinterpret_cast< std::add_pointer_t<qreal>>(_a[1]))); break;
        case 4: _t->addPacket((*reinterpret_cast< std::add_pointer_t<PacketInfo>>(_a[1]))); break;
        case 5: _t->clearPackets(); break;
        case 6: _t->updateNetworkTopology(); break;
        case 7: _t->resetView(); break;
        case 8: _t->zoomIn(); break;
        case 9: _t->zoomOut(); break;
        case 10: _t->zoomToFit(); break;
        case 11: _t->startAnimation(); break;
        case 12: _t->stopAnimation(); break;
        case 13: _t->pauseAnimation(); break;
        case 14: _t->onAnimationTimer(); break;
        case 15: _t->onNodeHovered((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 16: _t->onLinkHovered((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 17: _t->onPacketAnimationFinished(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
        case 2:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< PacketInfo >(); break;
            }
            break;
        case 4:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< PacketInfo >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (NetworkVisualizationWidget::*)(const QString & );
            if (_t _q_method = &NetworkVisualizationWidget::nodeClicked; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (NetworkVisualizationWidget::*)(const QString & );
            if (_t _q_method = &NetworkVisualizationWidget::linkClicked; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (NetworkVisualizationWidget::*)(const PacketInfo & );
            if (_t _q_method = &NetworkVisualizationWidget::packetClicked; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (NetworkVisualizationWidget::*)(qreal );
            if (_t _q_method = &NetworkVisualizationWidget::zoomChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
    }else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<NetworkVisualizationWidget *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< qreal*>(_v) = _t->zoomFactor(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<NetworkVisualizationWidget *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setZoomFactor(*reinterpret_cast< qreal*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    } else if (_c == QMetaObject::BindableProperty) {
    }
}

const QMetaObject *NetworkVisualizationWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *NetworkVisualizationWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_NetworkVisualizationWidget.stringdata0))
        return static_cast<void*>(this);
    return QGraphicsView::qt_metacast(_clname);
}

int NetworkVisualizationWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QGraphicsView::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 18)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 18;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 18)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 18;
    }else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 1;
    }
    return _id;
}

// SIGNAL 0
void NetworkVisualizationWidget::nodeClicked(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void NetworkVisualizationWidget::linkClicked(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void NetworkVisualizationWidget::packetClicked(const PacketInfo & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void NetworkVisualizationWidget::zoomChanged(qreal _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
