/****************************************************************************
** Meta object code from reading C++ file 'alertpanelwidget.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.4.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/ui/widgets/alertpanelwidget.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'alertpanelwidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.4.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
namespace {
struct qt_meta_stringdata_AlertPanelWidget_t {
    uint offsetsAndSizes[68];
    char stringdata0[17];
    char stringdata1[18];
    char stringdata2[1];
    char stringdata3[11];
    char stringdata4[6];
    char stringdata5[13];
    char stringdata6[8];
    char stringdata7[18];
    char stringdata8[15];
    char stringdata9[14];
    char stringdata10[14];
    char stringdata11[15];
    char stringdata12[12];
    char stringdata13[17];
    char stringdata14[9];
    char stringdata15[10];
    char stringdata16[6];
    char stringdata17[12];
    char stringdata18[12];
    char stringdata19[15];
    char stringdata20[17];
    char stringdata21[21];
    char stringdata22[13];
    char stringdata23[17];
    char stringdata24[14];
    char stringdata25[20];
    char stringdata26[15];
    char stringdata27[21];
    char stringdata28[17];
    char stringdata29[17];
    char stringdata30[24];
    char stringdata31[15];
    char stringdata32[16];
    char stringdata33[20];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_AlertPanelWidget_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_AlertPanelWidget_t qt_meta_stringdata_AlertPanelWidget = {
    {
        QT_MOC_LITERAL(0, 16),  // "AlertPanelWidget"
        QT_MOC_LITERAL(17, 17),  // "panelStateChanged"
        QT_MOC_LITERAL(35, 0),  // ""
        QT_MOC_LITERAL(36, 10),  // "PanelState"
        QT_MOC_LITERAL(47, 5),  // "state"
        QT_MOC_LITERAL(53, 12),  // "alertClicked"
        QT_MOC_LITERAL(66, 7),  // "alertId"
        QT_MOC_LITERAL(74, 17),  // "alertAcknowledged"
        QT_MOC_LITERAL(92, 14),  // "alertDismissed"
        QT_MOC_LITERAL(107, 13),  // "alertResolved"
        QT_MOC_LITERAL(121, 13),  // "panelExpanded"
        QT_MOC_LITERAL(135, 14),  // "panelCollapsed"
        QT_MOC_LITERAL(150, 11),  // "panelHidden"
        QT_MOC_LITERAL(162, 16),  // "allAlertsCleared"
        QT_MOC_LITERAL(179, 8),  // "addAlert"
        QT_MOC_LITERAL(188, 9),  // "AlertInfo"
        QT_MOC_LITERAL(198, 5),  // "alert"
        QT_MOC_LITERAL(204, 11),  // "updateAlert"
        QT_MOC_LITERAL(216, 11),  // "removeAlert"
        QT_MOC_LITERAL(228, 14),  // "clearAllAlerts"
        QT_MOC_LITERAL(243, 16),  // "acknowledgeAlert"
        QT_MOC_LITERAL(260, 20),  // "acknowledgeAllAlerts"
        QT_MOC_LITERAL(281, 12),  // "dismissAlert"
        QT_MOC_LITERAL(294, 16),  // "dismissAllAlerts"
        QT_MOC_LITERAL(311, 13),  // "refreshAlerts"
        QT_MOC_LITERAL(325, 19),  // "onAlertModelChanged"
        QT_MOC_LITERAL(345, 14),  // "onAlertClicked"
        QT_MOC_LITERAL(360, 20),  // "onAcknowledgeClicked"
        QT_MOC_LITERAL(381, 16),  // "onDismissClicked"
        QT_MOC_LITERAL(398, 16),  // "onResolveClicked"
        QT_MOC_LITERAL(415, 23),  // "onExpandCollapseClicked"
        QT_MOC_LITERAL(439, 14),  // "onCloseClicked"
        QT_MOC_LITERAL(454, 15),  // "onAutoHideTimer"
        QT_MOC_LITERAL(470, 19)   // "onAnimationFinished"
    },
    "AlertPanelWidget",
    "panelStateChanged",
    "",
    "PanelState",
    "state",
    "alertClicked",
    "alertId",
    "alertAcknowledged",
    "alertDismissed",
    "alertResolved",
    "panelExpanded",
    "panelCollapsed",
    "panelHidden",
    "allAlertsCleared",
    "addAlert",
    "AlertInfo",
    "alert",
    "updateAlert",
    "removeAlert",
    "clearAllAlerts",
    "acknowledgeAlert",
    "acknowledgeAllAlerts",
    "dismissAlert",
    "dismissAllAlerts",
    "refreshAlerts",
    "onAlertModelChanged",
    "onAlertClicked",
    "onAcknowledgeClicked",
    "onDismissClicked",
    "onResolveClicked",
    "onExpandCollapseClicked",
    "onCloseClicked",
    "onAutoHideTimer",
    "onAnimationFinished"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_AlertPanelWidget[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
      27,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       9,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    1,  176,    2, 0x06,    1 /* Public */,
       5,    1,  179,    2, 0x06,    3 /* Public */,
       7,    1,  182,    2, 0x06,    5 /* Public */,
       8,    1,  185,    2, 0x06,    7 /* Public */,
       9,    1,  188,    2, 0x06,    9 /* Public */,
      10,    0,  191,    2, 0x06,   11 /* Public */,
      11,    0,  192,    2, 0x06,   12 /* Public */,
      12,    0,  193,    2, 0x06,   13 /* Public */,
      13,    0,  194,    2, 0x06,   14 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      14,    1,  195,    2, 0x0a,   15 /* Public */,
      17,    1,  198,    2, 0x0a,   17 /* Public */,
      18,    1,  201,    2, 0x0a,   19 /* Public */,
      19,    0,  204,    2, 0x0a,   21 /* Public */,
      20,    1,  205,    2, 0x0a,   22 /* Public */,
      21,    0,  208,    2, 0x0a,   24 /* Public */,
      22,    1,  209,    2, 0x0a,   25 /* Public */,
      23,    0,  212,    2, 0x0a,   27 /* Public */,
      24,    0,  213,    2, 0x0a,   28 /* Public */,
      25,    0,  214,    2, 0x08,   29 /* Private */,
      26,    0,  215,    2, 0x08,   30 /* Private */,
      27,    0,  216,    2, 0x08,   31 /* Private */,
      28,    0,  217,    2, 0x08,   32 /* Private */,
      29,    0,  218,    2, 0x08,   33 /* Private */,
      30,    0,  219,    2, 0x08,   34 /* Private */,
      31,    0,  220,    2, 0x08,   35 /* Private */,
      32,    0,  221,    2, 0x08,   36 /* Private */,
      33,    0,  222,    2, 0x08,   37 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 15,   16,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject AlertPanelWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_AlertPanelWidget.offsetsAndSizes,
    qt_meta_data_AlertPanelWidget,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_AlertPanelWidget_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<AlertPanelWidget, std::true_type>,
        // method 'panelStateChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<PanelState, std::false_type>,
        // method 'alertClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'alertAcknowledged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'alertDismissed'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'alertResolved'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'panelExpanded'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'panelCollapsed'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'panelHidden'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'allAlertsCleared'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'addAlert'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const AlertInfo &, std::false_type>,
        // method 'updateAlert'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'removeAlert'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'clearAllAlerts'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'acknowledgeAlert'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'acknowledgeAllAlerts'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'dismissAlert'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'dismissAllAlerts'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'refreshAlerts'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onAlertModelChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onAlertClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onAcknowledgeClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onDismissClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onResolveClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onExpandCollapseClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onCloseClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onAutoHideTimer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onAnimationFinished'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void AlertPanelWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<AlertPanelWidget *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->panelStateChanged((*reinterpret_cast< std::add_pointer_t<PanelState>>(_a[1]))); break;
        case 1: _t->alertClicked((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 2: _t->alertAcknowledged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->alertDismissed((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 4: _t->alertResolved((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 5: _t->panelExpanded(); break;
        case 6: _t->panelCollapsed(); break;
        case 7: _t->panelHidden(); break;
        case 8: _t->allAlertsCleared(); break;
        case 9: _t->addAlert((*reinterpret_cast< std::add_pointer_t<AlertInfo>>(_a[1]))); break;
        case 10: _t->updateAlert((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 11: _t->removeAlert((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 12: _t->clearAllAlerts(); break;
        case 13: _t->acknowledgeAlert((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 14: _t->acknowledgeAllAlerts(); break;
        case 15: _t->dismissAlert((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 16: _t->dismissAllAlerts(); break;
        case 17: _t->refreshAlerts(); break;
        case 18: _t->onAlertModelChanged(); break;
        case 19: _t->onAlertClicked(); break;
        case 20: _t->onAcknowledgeClicked(); break;
        case 21: _t->onDismissClicked(); break;
        case 22: _t->onResolveClicked(); break;
        case 23: _t->onExpandCollapseClicked(); break;
        case 24: _t->onCloseClicked(); break;
        case 25: _t->onAutoHideTimer(); break;
        case 26: _t->onAnimationFinished(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
        case 9:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< AlertInfo >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (AlertPanelWidget::*)(PanelState );
            if (_t _q_method = &AlertPanelWidget::panelStateChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (AlertPanelWidget::*)(const QString & );
            if (_t _q_method = &AlertPanelWidget::alertClicked; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (AlertPanelWidget::*)(const QString & );
            if (_t _q_method = &AlertPanelWidget::alertAcknowledged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (AlertPanelWidget::*)(const QString & );
            if (_t _q_method = &AlertPanelWidget::alertDismissed; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (AlertPanelWidget::*)(const QString & );
            if (_t _q_method = &AlertPanelWidget::alertResolved; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (AlertPanelWidget::*)();
            if (_t _q_method = &AlertPanelWidget::panelExpanded; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (AlertPanelWidget::*)();
            if (_t _q_method = &AlertPanelWidget::panelCollapsed; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (AlertPanelWidget::*)();
            if (_t _q_method = &AlertPanelWidget::panelHidden; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 7;
                return;
            }
        }
        {
            using _t = void (AlertPanelWidget::*)();
            if (_t _q_method = &AlertPanelWidget::allAlertsCleared; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 8;
                return;
            }
        }
    }
}

const QMetaObject *AlertPanelWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AlertPanelWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_AlertPanelWidget.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int AlertPanelWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 27)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 27;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 27)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 27;
    }
    return _id;
}

// SIGNAL 0
void AlertPanelWidget::panelStateChanged(PanelState _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void AlertPanelWidget::alertClicked(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void AlertPanelWidget::alertAcknowledged(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void AlertPanelWidget::alertDismissed(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void AlertPanelWidget::alertResolved(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void AlertPanelWidget::panelExpanded()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void AlertPanelWidget::panelCollapsed()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void AlertPanelWidget::panelHidden()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}

// SIGNAL 8
void AlertPanelWidget::allAlertsCleared()
{
    QMetaObject::activate(this, &staticMetaObject, 8, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
