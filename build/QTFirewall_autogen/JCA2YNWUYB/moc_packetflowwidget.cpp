/****************************************************************************
** Meta object code from reading C++ file 'packetflowwidget.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.4.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/ui/widgets/packetflowwidget.h"
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'packetflowwidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.4.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
namespace {
struct qt_meta_stringdata_PacketFlowWidget_t {
    uint offsetsAndSizes[22];
    char stringdata0[17];
    char stringdata1[14];
    char stringdata2[1];
    char stringdata3[11];
    char stringdata4[7];
    char stringdata5[10];
    char stringdata6[13];
    char stringdata7[14];
    char stringdata8[15];
    char stringdata9[15];
    char stringdata10[16];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_PacketFlowWidget_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_PacketFlowWidget_t qt_meta_stringdata_PacketFlowWidget = {
    {
        QT_MOC_LITERAL(0, 16),  // "PacketFlowWidget"
        QT_MOC_LITERAL(17, 13),  // "packetClicked"
        QT_MOC_LITERAL(31, 0),  // ""
        QT_MOC_LITERAL(32, 10),  // "PacketInfo"
        QT_MOC_LITERAL(43, 6),  // "packet"
        QT_MOC_LITERAL(50, 9),  // "addPacket"
        QT_MOC_LITERAL(60, 12),  // "clearPackets"
        QT_MOC_LITERAL(73, 13),  // "playAnimation"
        QT_MOC_LITERAL(87, 14),  // "pauseAnimation"
        QT_MOC_LITERAL(102, 14),  // "resetAnimation"
        QT_MOC_LITERAL(117, 15)   // "updateAnimation"
    },
    "PacketFlowWidget",
    "packetClicked",
    "",
    "PacketInfo",
    "packet",
    "addPacket",
    "clearPackets",
    "playAnimation",
    "pauseAnimation",
    "resetAnimation",
    "updateAnimation"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_PacketFlowWidget[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
       7,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    1,   56,    2, 0x06,    1 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       5,    1,   59,    2, 0x0a,    3 /* Public */,
       6,    0,   62,    2, 0x0a,    5 /* Public */,
       7,    0,   63,    2, 0x0a,    6 /* Public */,
       8,    0,   64,    2, 0x0a,    7 /* Public */,
       9,    0,   65,    2, 0x0a,    8 /* Public */,
      10,    0,   66,    2, 0x08,    9 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject PacketFlowWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_PacketFlowWidget.offsetsAndSizes,
    qt_meta_data_PacketFlowWidget,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_PacketFlowWidget_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<PacketFlowWidget, std::true_type>,
        // method 'packetClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const PacketInfo &, std::false_type>,
        // method 'addPacket'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const PacketInfo &, std::false_type>,
        // method 'clearPackets'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'playAnimation'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'pauseAnimation'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'resetAnimation'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'updateAnimation'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void PacketFlowWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<PacketFlowWidget *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->packetClicked((*reinterpret_cast< std::add_pointer_t<PacketInfo>>(_a[1]))); break;
        case 1: _t->addPacket((*reinterpret_cast< std::add_pointer_t<PacketInfo>>(_a[1]))); break;
        case 2: _t->clearPackets(); break;
        case 3: _t->playAnimation(); break;
        case 4: _t->pauseAnimation(); break;
        case 5: _t->resetAnimation(); break;
        case 6: _t->updateAnimation(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
        case 0:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< PacketInfo >(); break;
            }
            break;
        case 1:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< PacketInfo >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (PacketFlowWidget::*)(const PacketInfo & );
            if (_t _q_method = &PacketFlowWidget::packetClicked; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
    }
}

const QMetaObject *PacketFlowWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *PacketFlowWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_PacketFlowWidget.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int PacketFlowWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    }
    return _id;
}

// SIGNAL 0
void PacketFlowWidget::packetClicked(const PacketInfo & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
