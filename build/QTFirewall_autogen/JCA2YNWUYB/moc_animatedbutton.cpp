/****************************************************************************
** Meta object code from reading C++ file 'animatedbutton.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.4.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/ui/widgets/animatedbutton.h"
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'animatedbutton.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.4.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
namespace {
struct qt_meta_stringdata_AnimatedButton_t {
    uint offsetsAndSizes[30];
    char stringdata0[15];
    char stringdata1[18];
    char stringdata2[1];
    char stringdata3[13];
    char stringdata4[10];
    char stringdata5[20];
    char stringdata6[19];
    char stringdata7[13];
    char stringdata8[11];
    char stringdata9[25];
    char stringdata10[25];
    char stringdata11[25];
    char stringdata12[6];
    char stringdata13[11];
    char stringdata14[8];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_AnimatedButton_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_AnimatedButton_t qt_meta_stringdata_AnimatedButton = {
    {
        QT_MOC_LITERAL(0, 14),  // "AnimatedButton"
        QT_MOC_LITERAL(15, 17),  // "animationFinished"
        QT_MOC_LITERAL(33, 0),  // ""
        QT_MOC_LITERAL(34, 12),  // "hoverEntered"
        QT_MOC_LITERAL(47, 9),  // "hoverLeft"
        QT_MOC_LITERAL(57, 19),  // "startPulseAnimation"
        QT_MOC_LITERAL(77, 18),  // "stopPulseAnimation"
        QT_MOC_LITERAL(96, 12),  // "flashSuccess"
        QT_MOC_LITERAL(109, 10),  // "flashError"
        QT_MOC_LITERAL(120, 24),  // "onHoverAnimationFinished"
        QT_MOC_LITERAL(145, 24),  // "onClickAnimationFinished"
        QT_MOC_LITERAL(170, 24),  // "onPulseAnimationFinished"
        QT_MOC_LITERAL(195, 5),  // "scale"
        QT_MOC_LITERAL(201, 10),  // "glowRadius"
        QT_MOC_LITERAL(212, 7)   // "opacity"
    },
    "AnimatedButton",
    "animationFinished",
    "",
    "hoverEntered",
    "hoverLeft",
    "startPulseAnimation",
    "stopPulseAnimation",
    "flashSuccess",
    "flashError",
    "onHoverAnimationFinished",
    "onClickAnimationFinished",
    "onPulseAnimationFinished",
    "scale",
    "glowRadius",
    "opacity"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_AnimatedButton[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
      10,   14, // methods
       3,   84, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,   74,    2, 0x06,    4 /* Public */,
       3,    0,   75,    2, 0x06,    5 /* Public */,
       4,    0,   76,    2, 0x06,    6 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       5,    0,   77,    2, 0x0a,    7 /* Public */,
       6,    0,   78,    2, 0x0a,    8 /* Public */,
       7,    0,   79,    2, 0x0a,    9 /* Public */,
       8,    0,   80,    2, 0x0a,   10 /* Public */,
       9,    0,   81,    2, 0x08,   11 /* Private */,
      10,    0,   82,    2, 0x08,   12 /* Private */,
      11,    0,   83,    2, 0x08,   13 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

 // properties: name, type, flags
      12, QMetaType::QReal, 0x00015103, uint(-1), 0,
      13, QMetaType::QReal, 0x00015103, uint(-1), 0,
      14, QMetaType::QReal, 0x00015103, uint(-1), 0,

       0        // eod
};

Q_CONSTINIT const QMetaObject AnimatedButton::staticMetaObject = { {
    QMetaObject::SuperData::link<QPushButton::staticMetaObject>(),
    qt_meta_stringdata_AnimatedButton.offsetsAndSizes,
    qt_meta_data_AnimatedButton,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_AnimatedButton_t,
        // property 'scale'
        QtPrivate::TypeAndForceComplete<qreal, std::true_type>,
        // property 'glowRadius'
        QtPrivate::TypeAndForceComplete<qreal, std::true_type>,
        // property 'opacity'
        QtPrivate::TypeAndForceComplete<qreal, std::true_type>,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<AnimatedButton, std::true_type>,
        // method 'animationFinished'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'hoverEntered'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'hoverLeft'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'startPulseAnimation'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'stopPulseAnimation'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'flashSuccess'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'flashError'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onHoverAnimationFinished'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onClickAnimationFinished'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onPulseAnimationFinished'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void AnimatedButton::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<AnimatedButton *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->animationFinished(); break;
        case 1: _t->hoverEntered(); break;
        case 2: _t->hoverLeft(); break;
        case 3: _t->startPulseAnimation(); break;
        case 4: _t->stopPulseAnimation(); break;
        case 5: _t->flashSuccess(); break;
        case 6: _t->flashError(); break;
        case 7: _t->onHoverAnimationFinished(); break;
        case 8: _t->onClickAnimationFinished(); break;
        case 9: _t->onPulseAnimationFinished(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (AnimatedButton::*)();
            if (_t _q_method = &AnimatedButton::animationFinished; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (AnimatedButton::*)();
            if (_t _q_method = &AnimatedButton::hoverEntered; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (AnimatedButton::*)();
            if (_t _q_method = &AnimatedButton::hoverLeft; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
    }else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<AnimatedButton *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< qreal*>(_v) = _t->scale(); break;
        case 1: *reinterpret_cast< qreal*>(_v) = _t->glowRadius(); break;
        case 2: *reinterpret_cast< qreal*>(_v) = _t->opacity(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<AnimatedButton *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setScale(*reinterpret_cast< qreal*>(_v)); break;
        case 1: _t->setGlowRadius(*reinterpret_cast< qreal*>(_v)); break;
        case 2: _t->setOpacity(*reinterpret_cast< qreal*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    } else if (_c == QMetaObject::BindableProperty) {
    }
    (void)_a;
}

const QMetaObject *AnimatedButton::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AnimatedButton::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_AnimatedButton.stringdata0))
        return static_cast<void*>(this);
    return QPushButton::qt_metacast(_clname);
}

int AnimatedButton::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QPushButton::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 10)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 10)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 10;
    }else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    }
    return _id;
}

// SIGNAL 0
void AnimatedButton::animationFinished()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void AnimatedButton::hoverEntered()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void AnimatedButton::hoverLeft()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
