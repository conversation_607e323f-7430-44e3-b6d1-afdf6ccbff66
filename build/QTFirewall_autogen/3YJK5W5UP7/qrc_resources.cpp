/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 6.4.2
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

static const unsigned char qt_resource_data[] = {
  // /home/<USER>/Desktop/dev/QT_Firewall/resources/themes/dark.qss
  0x0,0x0,0x1,0xee,
  0x28,
  0xb5,0x2f,0xfd,0x60,0xe9,0x5,0x25,0xf,0x0,0x76,0x56,0x44,0x1f,0x30,0x73,0x1b,
  0xe4,0xbf,0xc9,0x1d,0x73,0xab,0x23,0x9a,0xca,0x2e,0x1f,0x34,0x77,0x6d,0x4a,0x3a,
  0x25,0x32,0x1f,0x2a,0xef,0x2a,0xe8,0x83,0x1,0x9c,0xa,0x6,0x3d,0x0,0x39,0x0,
  0x3c,0x0,0xd1,0x42,0xb3,0x18,0x50,0x6f,0x7a,0x13,0x40,0x9b,0x74,0xe8,0x4e,0x7a,
  0x31,0x73,0x69,0x6d,0xdc,0x56,0x1a,0x81,0xd4,0xe3,0x2f,0x76,0x26,0xf6,0x4e,0x2a,
  0x51,0x24,0x8,0x68,0xf4,0xfe,0x75,0xd,0x31,0x8c,0xa1,0x10,0xae,0x87,0x65,0x43,
  0xf4,0x52,0x51,0xe9,0x1a,0x65,0xe,0x5f,0x17,0xdd,0xc8,0x75,0x68,0x40,0x10,0x1c,
  0x3d,0x6a,0x83,0xee,0x17,0xe7,0x16,0x31,0x18,0x3c,0x7e,0xc7,0x90,0x67,0xfb,0x60,
  0x89,0x82,0x36,0x2b,0xf6,0x7b,0x8b,0x7d,0x64,0xb9,0xd8,0x8c,0x2b,0x9f,0xc3,0xdf,
  0xad,0x19,0x0,0xfa,0x1a,0xfa,0xd4,0xf,0x46,0xd0,0xc6,0xf1,0x7f,0xaf,0x24,0xc9,
  0x4f,0xba,0xa3,0xe4,0x36,0xb2,0xc8,0xa1,0x70,0xf,0x6,0x6e,0xc,0xc3,0x6a,0x59,
  0xb1,0x86,0x39,0x59,0x62,0x55,0x85,0x40,0x10,0x8,0xf,0x7a,0xa5,0xed,0x2f,0xab,
  0xd9,0x23,0x7d,0x74,0xcd,0x81,0xa0,0x4e,0x41,0x1d,0xf8,0x9a,0x19,0xe5,0xd9,0x7a,
  0x9d,0xd9,0xc6,0xcc,0x7e,0xdf,0x22,0xb9,0xe3,0x57,0x9b,0x59,0xbf,0x95,0xd1,0xff,
  0xb3,0xf,0x46,0xb9,0x20,0x9d,0x99,0x73,0x4d,0xfb,0x7e,0xf,0x8e,0x2d,0x61,0xc5,
  0x60,0xe2,0x4b,0x8a,0xcd,0x49,0x1c,0x18,0xbe,0xb8,0x57,0x9b,0xae,0x7f,0xed,0x81,
  0xeb,0xfa,0x25,0x77,0x20,0x74,0xe9,0x2c,0xb3,0x35,0xc9,0x6f,0x39,0x22,0x45,0xef,
  0xa9,0xc0,0x14,0x73,0x6c,0xf8,0x5a,0x5a,0x12,0xff,0x46,0xc9,0x1f,0x53,0xa0,0x91,
  0x15,0x52,0x48,0x49,0x53,0x9a,0x1a,0xe5,0x72,0x30,0xc4,0x8,0x64,0xd0,0x99,0x7,
  0x69,0x48,0x40,0x6c,0x70,0xe4,0x20,0xae,0x8a,0xba,0xe2,0x58,0x36,0x12,0x3f,0x9a,
  0x34,0x0,0x5e,0x59,0x9,0xd,0x97,0xb1,0x4e,0x5f,0x38,0xf7,0xe0,0x86,0x16,0x23,
  0xab,0xb0,0x92,0x49,0xe5,0x49,0x26,0x48,0xc,0x6b,0xcc,0x0,0xd4,0x73,0x30,0xc6,
  0xfb,0xfc,0x32,0x88,0x88,0xd4,0x98,0x63,0x86,0x75,0xa8,0x9d,0xa4,0x2d,0x3a,0x3a,
  0x84,0x3c,0x6e,0xc3,0x8a,0x60,0x69,0x17,0x6d,0x54,0xd2,0x60,0x86,0x3c,0xf3,0xc6,
  0xf3,0x50,0x73,0x3e,0x4c,0x1,0x53,0xf7,0x32,0xf3,0x8c,0xf,0xb5,0xd8,0xdc,0x1c,
  0x96,0xc8,0xee,0x87,0x87,0x58,0xd8,0xdd,0x72,0xbb,0xa2,0x84,0xe4,0x70,0x30,0x5e,
  0xb1,0xbc,0x4d,0xcf,0x5a,0x90,0x1d,0xb2,0x51,0xf5,0x3c,0x66,0xcc,0x8,0xc2,0xb6,
  0xc5,0x41,0x27,0xc8,0xe6,0x78,0xe3,0x31,0xb5,0xc3,0xa6,0xb6,0xa5,0xe4,0x12,0x62,
  0x40,0xb5,0xc1,0xf7,0x21,0xcc,0xb,0xe6,0x3b,0x94,0xc6,0xd8,0x8,0x99,0x54,0x4d,
  0xe0,0x83,0xf,0x81,0x5f,0x52,0xe9,0x8c,0xf0,0xd5,0xea,0x0,0xfe,0xca,0x22,0xb1,
  0xe9,0xf1,0x98,0x75,0xe2,0x1b,0x22,0xa,0xbd,0xd4,0x6,0x40,0x15,
    // /home/<USER>/Desktop/dev/QT_Firewall/resources/themes/light.qss
  0x0,0x0,0x1,0xb2,
  0x28,
  0xb5,0x2f,0xfd,0x60,0x1b,0x5,0x45,0xd,0x0,0x6,0x93,0x3a,0x1e,0x30,0x73,0x1b,
  0xe4,0xbf,0xc9,0x9d,0x55,0xaa,0x4b,0xa6,0xb2,0xc6,0x7,0xcd,0x5d,0x9b,0x52,0x92,
  0xae,0xff,0x6f,0xdf,0x15,0xfe,0xa4,0xa1,0x4c,0x94,0xa,0x33,0x0,0x31,0x0,0x31,
  0x0,0xce,0x2b,0xa9,0xf5,0xe5,0xd0,0xda,0x54,0x4a,0x65,0x4,0x4a,0x8f,0xb7,0x66,
  0x4c,0xb3,0x4a,0x28,0x51,0x24,0x8,0x64,0x3b,0xbf,0x56,0xec,0xde,0x63,0xa4,0x73,
  0xea,0x59,0xc8,0x8f,0xb4,0x50,0x12,0x7a,0x26,0x97,0xb3,0x1a,0x4b,0xbe,0xc6,0xe2,
  0x48,0x6,0x4,0x1,0x91,0x7d,0x5e,0xa2,0x90,0xcc,0x6a,0x6e,0x4d,0xdf,0xf7,0x42,
  0x7d,0xb1,0x15,0x3e,0x67,0x9f,0x19,0xab,0x0,0xd9,0xd8,0x79,0xcc,0xcf,0x1b,0x24,
  0xe3,0xf8,0xbf,0x53,0x12,0xdc,0x6,0x7d,0x82,0xda,0xb8,0x7a,0x43,0xd1,0x62,0x5a,
  0x64,0x0,0xc9,0xa0,0x7,0x54,0xb2,0xf5,0x48,0xdf,0x9e,0x39,0xc,0x52,0x13,0xaf,
  0xc9,0xaf,0xd5,0x18,0xa3,0x8d,0x78,0xbd,0x7e,0x5,0x6e,0xc7,0x35,0x2c,0x5e,0xdc,
  0x14,0xb6,0xff,0x5f,0x9f,0x47,0xa8,0xf4,0xc5,0x6d,0xef,0x64,0xce,0xdd,0x52,0x68,
  0x9,0x44,0xdb,0xaf,0xd6,0x6d,0x51,0x8,0x94,0xf1,0xaa,0x3c,0xc3,0xb8,0x39,0xb6,
  0x39,0x12,0x19,0x83,0xdb,0x74,0x21,0x52,0x3b,0x4f,0xbb,0xcd,0xae,0x31,0xb4,0x12,
  0xd2,0xbe,0x36,0xdc,0x77,0xba,0x7c,0x59,0x47,0xe9,0x2a,0xd4,0x10,0xe3,0xc2,0xb7,
  0xd4,0x1e,0xa6,0x41,0x73,0x1e,0x4c,0xa0,0xe1,0x11,0x51,0xcc,0x20,0x91,0xa6,0xd6,
  0x30,0x7,0x40,0x84,0x8,0x84,0xa0,0x3b,0xf,0x72,0x4c,0x20,0x8e,0x39,0x7c,0xbc,
  0xfe,0xa0,0x8c,0x48,0x32,0x1f,0x20,0x93,0xe8,0xe,0xa,0xb5,0x9d,0x8a,0xb4,0x6c,
  0x15,0x8,0xef,0x0,0xff,0x9,0xea,0x76,0xf4,0x19,0x39,0xb4,0x80,0x3,0xde,0x92,
  0x40,0xe8,0xac,0x23,0xf6,0x82,0x95,0xca,0xc1,0x4d,0xbc,0x1,0x0,0x15,0x77,0x30,
  0xe,0x2a,0xb,0xed,0x89,0x57,0x9,0x6d,0xb8,0xed,0xd5,0x8a,0xce,0xcd,0xb0,0x85,
  0xec,0x67,0xb7,0x23,0x2b,0xc8,0xf9,0x88,0xe0,0xd0,0xa0,0xdb,0x78,0x72,0x1e,0xec,
  0xe,0xfd,0x70,0xaf,0xe4,0xf9,0x30,0x22,0xe1,0xe,0x9d,0xcf,0x9c,0x60,0xc0,0x7a,
  0xd3,0xe5,0x1f,0x0,0xf0,0xb0,0xf5,0xe8,0xdd,0x43,0xa7,0xb6,0xa1,0xea,0xc5,0xda,
  0x70,0xfe,0x61,0x7,0xf9,0x29,0xd7,0x8d,0xf6,0x7f,0x2c,0x7b,0x0,0x32,0x95,0x81,
  0x4e,0x98,0x36,0x48,0x41,0x6,0xc2,0x5d,0xaa,0xe2,0x5a,0xe1,0x63,0xd3,0x72,0x3a,
  0x63,0x20,0xc2,0xa0,0xc3,0xac,0xb0,0x8,0x37,0x84,0x22,0xf7,0x71,0xfb,0x3b,0x70,
  0xab,
  
};

static const unsigned char qt_resource_name[] = {
  // themes
  0x0,0x6,
  0x7,0xae,0xc3,0xc3,
  0x0,0x74,
  0x0,0x68,0x0,0x65,0x0,0x6d,0x0,0x65,0x0,0x73,
    // dark.qss
  0x0,0x8,
  0x8,0x8e,0x55,0xe3,
  0x0,0x64,
  0x0,0x61,0x0,0x72,0x0,0x6b,0x0,0x2e,0x0,0x71,0x0,0x73,0x0,0x73,
    // light.qss
  0x0,0x9,
  0xd,0xf7,0xbd,0x43,
  0x0,0x6c,
  0x0,0x69,0x0,0x67,0x0,0x68,0x0,0x74,0x0,0x2e,0x0,0x71,0x0,0x73,0x0,0x73,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/themes
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x2,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/themes/themes
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x3,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/themes/themes/dark.qss
  0x0,0x0,0x0,0x12,0x0,0x4,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x98,0x2f,0x21,0xf6,0x84,
  // :/themes/themes/light.qss
  0x0,0x0,0x0,0x28,0x0,0x4,0x0,0x0,0x0,0x1,0x0,0x0,0x1,0xf2,
0x0,0x0,0x1,0x98,0x2f,0x22,0x3e,0x14,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#ifdef QT_NAMESPACE
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#if defined(__ELF__) || defined(__APPLE__)
static inline unsigned char qResourceFeatureZstd()
{
    extern const unsigned char qt_resourceFeatureZstd;
    return qt_resourceFeatureZstd;
}
#else
unsigned char qResourceFeatureZstd();
#endif

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)()
{
    int version = 3;
    version += QT_RCC_PREPEND_NAMESPACE(qResourceFeatureZstd());
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)(); }
   } dummy;
}
