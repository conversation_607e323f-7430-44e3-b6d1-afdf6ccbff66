/****************************************************************************
** Meta object code from reading C++ file 'dashboardview.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.4.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/ui/dashboardview.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'dashboardview.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.4.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
namespace {
struct qt_meta_stringdata_DashboardView_t {
    uint offsetsAndSizes[42];
    char stringdata0[14];
    char stringdata1[18];
    char stringdata2[1];
    char stringdata3[18];
    char stringdata4[13];
    char stringdata5[9];
    char stringdata6[13];
    char stringdata7[7];
    char stringdata8[16];
    char stringdata9[15];
    char stringdata10[10];
    char stringdata11[11];
    char stringdata12[12];
    char stringdata13[17];
    char stringdata14[24];
    char stringdata15[17];
    char stringdata16[11];
    char stringdata17[7];
    char stringdata18[21];
    char stringdata19[20];
    char stringdata20[17];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_DashboardView_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_DashboardView_t qt_meta_stringdata_DashboardView = {
    {
        QT_MOC_LITERAL(0, 13),  // "DashboardView"
        QT_MOC_LITERAL(14, 17),  // "monitoringStarted"
        QT_MOC_LITERAL(32, 0),  // ""
        QT_MOC_LITERAL(33, 17),  // "monitoringStopped"
        QT_MOC_LITERAL(51, 12),  // "dataExported"
        QT_MOC_LITERAL(64, 8),  // "filename"
        QT_MOC_LITERAL(73, 12),  // "nodeSelected"
        QT_MOC_LITERAL(86, 6),  // "nodeId"
        QT_MOC_LITERAL(93, 15),  // "startMonitoring"
        QT_MOC_LITERAL(109, 14),  // "stopMonitoring"
        QT_MOC_LITERAL(124, 9),  // "clearData"
        QT_MOC_LITERAL(134, 10),  // "exportData"
        QT_MOC_LITERAL(145, 11),  // "refreshView"
        QT_MOC_LITERAL(157, 16),  // "updateStatistics"
        QT_MOC_LITERAL(174, 23),  // "onProtocolFilterChanged"
        QT_MOC_LITERAL(198, 16),  // "onPacketReceived"
        QT_MOC_LITERAL(215, 10),  // "PacketInfo"
        QT_MOC_LITERAL(226, 6),  // "packet"
        QT_MOC_LITERAL(233, 20),  // "onNetworkNodeClicked"
        QT_MOC_LITERAL(254, 19),  // "onAnimationFinished"
        QT_MOC_LITERAL(274, 16)   // "toggleFullscreen"
    },
    "DashboardView",
    "monitoringStarted",
    "",
    "monitoringStopped",
    "dataExported",
    "filename",
    "nodeSelected",
    "nodeId",
    "startMonitoring",
    "stopMonitoring",
    "clearData",
    "exportData",
    "refreshView",
    "updateStatistics",
    "onProtocolFilterChanged",
    "onPacketReceived",
    "PacketInfo",
    "packet",
    "onNetworkNodeClicked",
    "onAnimationFinished",
    "toggleFullscreen"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_DashboardView[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
      15,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,  104,    2, 0x06,    1 /* Public */,
       3,    0,  105,    2, 0x06,    2 /* Public */,
       4,    1,  106,    2, 0x06,    3 /* Public */,
       6,    1,  109,    2, 0x06,    5 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       8,    0,  112,    2, 0x0a,    7 /* Public */,
       9,    0,  113,    2, 0x0a,    8 /* Public */,
      10,    0,  114,    2, 0x0a,    9 /* Public */,
      11,    0,  115,    2, 0x0a,   10 /* Public */,
      12,    0,  116,    2, 0x0a,   11 /* Public */,
      13,    0,  117,    2, 0x08,   12 /* Private */,
      14,    0,  118,    2, 0x08,   13 /* Private */,
      15,    1,  119,    2, 0x08,   14 /* Private */,
      18,    1,  122,    2, 0x08,   16 /* Private */,
      19,    0,  125,    2, 0x08,   18 /* Private */,
      20,    0,  126,    2, 0x08,   19 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void, QMetaType::QString,    7,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 16,   17,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject DashboardView::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_DashboardView.offsetsAndSizes,
    qt_meta_data_DashboardView,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_DashboardView_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<DashboardView, std::true_type>,
        // method 'monitoringStarted'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'monitoringStopped'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'dataExported'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'nodeSelected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'startMonitoring'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'stopMonitoring'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'clearData'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'exportData'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'refreshView'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'updateStatistics'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onProtocolFilterChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onPacketReceived'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const PacketInfo &, std::false_type>,
        // method 'onNetworkNodeClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'onAnimationFinished'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'toggleFullscreen'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void DashboardView::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<DashboardView *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->monitoringStarted(); break;
        case 1: _t->monitoringStopped(); break;
        case 2: _t->dataExported((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->nodeSelected((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 4: _t->startMonitoring(); break;
        case 5: _t->stopMonitoring(); break;
        case 6: _t->clearData(); break;
        case 7: _t->exportData(); break;
        case 8: _t->refreshView(); break;
        case 9: _t->updateStatistics(); break;
        case 10: _t->onProtocolFilterChanged(); break;
        case 11: _t->onPacketReceived((*reinterpret_cast< std::add_pointer_t<PacketInfo>>(_a[1]))); break;
        case 12: _t->onNetworkNodeClicked((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 13: _t->onAnimationFinished(); break;
        case 14: _t->toggleFullscreen(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (DashboardView::*)();
            if (_t _q_method = &DashboardView::monitoringStarted; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (DashboardView::*)();
            if (_t _q_method = &DashboardView::monitoringStopped; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (DashboardView::*)(const QString & );
            if (_t _q_method = &DashboardView::dataExported; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (DashboardView::*)(const QString & );
            if (_t _q_method = &DashboardView::nodeSelected; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
    }
}

const QMetaObject *DashboardView::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *DashboardView::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_DashboardView.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int DashboardView::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 15)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 15;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 15)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 15;
    }
    return _id;
}

// SIGNAL 0
void DashboardView::monitoringStarted()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void DashboardView::monitoringStopped()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void DashboardView::dataExported(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void DashboardView::nodeSelected(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
