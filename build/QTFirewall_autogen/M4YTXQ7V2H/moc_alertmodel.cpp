/****************************************************************************
** Meta object code from reading C++ file 'alertmodel.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.4.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/models/alertmodel.h"
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'alertmodel.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.4.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
namespace {
struct qt_meta_stringdata_AlertModel_t {
    uint offsetsAndSizes[34];
    char stringdata0[11];
    char stringdata1[11];
    char stringdata2[1];
    char stringdata3[8];
    char stringdata4[13];
    char stringdata5[13];
    char stringdata6[18];
    char stringdata7[14];
    char stringdata8[15];
    char stringdata9[17];
    char stringdata10[10];
    char stringdata11[6];
    char stringdata12[18];
    char stringdata13[13];
    char stringdata14[13];
    char stringdata15[17];
    char stringdata16[23];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_AlertModel_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_AlertModel_t qt_meta_stringdata_AlertModel = {
    {
        QT_MOC_LITERAL(0, 10),  // "AlertModel"
        QT_MOC_LITERAL(11, 10),  // "alertAdded"
        QT_MOC_LITERAL(22, 0),  // ""
        QT_MOC_LITERAL(23, 7),  // "alertId"
        QT_MOC_LITERAL(31, 12),  // "alertUpdated"
        QT_MOC_LITERAL(44, 12),  // "alertRemoved"
        QT_MOC_LITERAL(57, 17),  // "alertAcknowledged"
        QT_MOC_LITERAL(75, 13),  // "alertResolved"
        QT_MOC_LITERAL(89, 14),  // "alertDismissed"
        QT_MOC_LITERAL(104, 16),  // "newCriticalAlert"
        QT_MOC_LITERAL(121, 9),  // "AlertInfo"
        QT_MOC_LITERAL(131, 5),  // "alert"
        QT_MOC_LITERAL(137, 17),  // "statisticsChanged"
        QT_MOC_LITERAL(155, 12),  // "modelChanged"
        QT_MOC_LITERAL(168, 12),  // "refreshModel"
        QT_MOC_LITERAL(181, 16),  // "cleanupOldAlerts"
        QT_MOC_LITERAL(198, 22)   // "aggregateSimilarAlerts"
    },
    "AlertModel",
    "alertAdded",
    "",
    "alertId",
    "alertUpdated",
    "alertRemoved",
    "alertAcknowledged",
    "alertResolved",
    "alertDismissed",
    "newCriticalAlert",
    "AlertInfo",
    "alert",
    "statisticsChanged",
    "modelChanged",
    "refreshModel",
    "cleanupOldAlerts",
    "aggregateSimilarAlerts"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_AlertModel[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
      12,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       9,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    1,   86,    2, 0x06,    1 /* Public */,
       4,    1,   89,    2, 0x06,    3 /* Public */,
       5,    1,   92,    2, 0x06,    5 /* Public */,
       6,    1,   95,    2, 0x06,    7 /* Public */,
       7,    1,   98,    2, 0x06,    9 /* Public */,
       8,    1,  101,    2, 0x06,   11 /* Public */,
       9,    1,  104,    2, 0x06,   13 /* Public */,
      12,    0,  107,    2, 0x06,   15 /* Public */,
      13,    0,  108,    2, 0x06,   16 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      14,    0,  109,    2, 0x0a,   17 /* Public */,
      15,    0,  110,    2, 0x0a,   18 /* Public */,
      16,    0,  111,    2, 0x0a,   19 /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, 0x80000000 | 10,   11,
    QMetaType::Void,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject AlertModel::staticMetaObject = { {
    QMetaObject::SuperData::link<QAbstractListModel::staticMetaObject>(),
    qt_meta_stringdata_AlertModel.offsetsAndSizes,
    qt_meta_data_AlertModel,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_AlertModel_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<AlertModel, std::true_type>,
        // method 'alertAdded'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'alertUpdated'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'alertRemoved'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'alertAcknowledged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'alertResolved'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'alertDismissed'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'newCriticalAlert'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const AlertInfo &, std::false_type>,
        // method 'statisticsChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'modelChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'refreshModel'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'cleanupOldAlerts'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'aggregateSimilarAlerts'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void AlertModel::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<AlertModel *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->alertAdded((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 1: _t->alertUpdated((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 2: _t->alertRemoved((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->alertAcknowledged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 4: _t->alertResolved((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 5: _t->alertDismissed((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 6: _t->newCriticalAlert((*reinterpret_cast< std::add_pointer_t<AlertInfo>>(_a[1]))); break;
        case 7: _t->statisticsChanged(); break;
        case 8: _t->modelChanged(); break;
        case 9: _t->refreshModel(); break;
        case 10: _t->cleanupOldAlerts(); break;
        case 11: _t->aggregateSimilarAlerts(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
        case 6:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< AlertInfo >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (AlertModel::*)(const QString & );
            if (_t _q_method = &AlertModel::alertAdded; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (AlertModel::*)(const QString & );
            if (_t _q_method = &AlertModel::alertUpdated; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (AlertModel::*)(const QString & );
            if (_t _q_method = &AlertModel::alertRemoved; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (AlertModel::*)(const QString & );
            if (_t _q_method = &AlertModel::alertAcknowledged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (AlertModel::*)(const QString & );
            if (_t _q_method = &AlertModel::alertResolved; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (AlertModel::*)(const QString & );
            if (_t _q_method = &AlertModel::alertDismissed; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (AlertModel::*)(const AlertInfo & );
            if (_t _q_method = &AlertModel::newCriticalAlert; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (AlertModel::*)();
            if (_t _q_method = &AlertModel::statisticsChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 7;
                return;
            }
        }
        {
            using _t = void (AlertModel::*)();
            if (_t _q_method = &AlertModel::modelChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 8;
                return;
            }
        }
    }
}

const QMetaObject *AlertModel::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AlertModel::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_AlertModel.stringdata0))
        return static_cast<void*>(this);
    return QAbstractListModel::qt_metacast(_clname);
}

int AlertModel::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QAbstractListModel::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 12)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 12)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    }
    return _id;
}

// SIGNAL 0
void AlertModel::alertAdded(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void AlertModel::alertUpdated(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void AlertModel::alertRemoved(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void AlertModel::alertAcknowledged(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void AlertModel::alertResolved(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void AlertModel::alertDismissed(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void AlertModel::newCriticalAlert(const AlertInfo & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void AlertModel::statisticsChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}

// SIGNAL 8
void AlertModel::modelChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 8, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
