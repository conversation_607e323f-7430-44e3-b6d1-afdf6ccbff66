/****************************************************************************
** Meta object code from reading C++ file 'configurationmodel.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.4.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/models/configurationmodel.h"
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'configurationmodel.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.4.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
namespace {
struct qt_meta_stringdata_ConfigurationModel_t {
    uint offsetsAndSizes[42];
    char stringdata0[19];
    char stringdata1[13];
    char stringdata2[1];
    char stringdata3[4];
    char stringdata4[9];
    char stringdata5[9];
    char stringdata6[16];
    char stringdata7[18];
    char stringdata8[18];
    char stringdata9[16];
    char stringdata10[15];
    char stringdata11[9];
    char stringdata12[22];
    char stringdata13[11];
    char stringdata14[16];
    char stringdata15[6];
    char stringdata16[13];
    char stringdata17[13];
    char stringdata18[13];
    char stringdata19[15];
    char stringdata20[9];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_ConfigurationModel_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_ConfigurationModel_t qt_meta_stringdata_ConfigurationModel = {
    {
        QT_MOC_LITERAL(0, 18),  // "ConfigurationModel"
        QT_MOC_LITERAL(19, 12),  // "valueChanged"
        QT_MOC_LITERAL(32, 0),  // ""
        QT_MOC_LITERAL(33, 3),  // "key"
        QT_MOC_LITERAL(37, 8),  // "oldValue"
        QT_MOC_LITERAL(46, 8),  // "newValue"
        QT_MOC_LITERAL(55, 15),  // "configItemAdded"
        QT_MOC_LITERAL(71, 17),  // "configItemRemoved"
        QT_MOC_LITERAL(89, 17),  // "configItemUpdated"
        QT_MOC_LITERAL(107, 15),  // "categoryChanged"
        QT_MOC_LITERAL(123, 14),  // "ConfigCategory"
        QT_MOC_LITERAL(138, 8),  // "category"
        QT_MOC_LITERAL(147, 21),  // "unsavedChangesChanged"
        QT_MOC_LITERAL(169, 10),  // "hasChanges"
        QT_MOC_LITERAL(180, 15),  // "validationError"
        QT_MOC_LITERAL(196, 5),  // "error"
        QT_MOC_LITERAL(202, 12),  // "modelChanged"
        QT_MOC_LITERAL(215, 12),  // "refreshModel"
        QT_MOC_LITERAL(228, 12),  // "applyChanges"
        QT_MOC_LITERAL(241, 14),  // "discardChanges"
        QT_MOC_LITERAL(256, 8)   // "autoSave"
    },
    "ConfigurationModel",
    "valueChanged",
    "",
    "key",
    "oldValue",
    "newValue",
    "configItemAdded",
    "configItemRemoved",
    "configItemUpdated",
    "categoryChanged",
    "ConfigCategory",
    "category",
    "unsavedChangesChanged",
    "hasChanges",
    "validationError",
    "error",
    "modelChanged",
    "refreshModel",
    "applyChanges",
    "discardChanges",
    "autoSave"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_ConfigurationModel[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
      12,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       8,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    3,   86,    2, 0x06,    1 /* Public */,
       6,    1,   93,    2, 0x06,    5 /* Public */,
       7,    1,   96,    2, 0x06,    7 /* Public */,
       8,    1,   99,    2, 0x06,    9 /* Public */,
       9,    1,  102,    2, 0x06,   11 /* Public */,
      12,    1,  105,    2, 0x06,   13 /* Public */,
      14,    2,  108,    2, 0x06,   15 /* Public */,
      16,    0,  113,    2, 0x06,   18 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      17,    0,  114,    2, 0x0a,   19 /* Public */,
      18,    0,  115,    2, 0x0a,   20 /* Public */,
      19,    0,  116,    2, 0x0a,   21 /* Public */,
      20,    0,  117,    2, 0x0a,   22 /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString, QMetaType::QVariant, QMetaType::QVariant,    3,    4,    5,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, 0x80000000 | 10,   11,
    QMetaType::Void, QMetaType::Bool,   13,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,    3,   15,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject ConfigurationModel::staticMetaObject = { {
    QMetaObject::SuperData::link<QAbstractItemModel::staticMetaObject>(),
    qt_meta_stringdata_ConfigurationModel.offsetsAndSizes,
    qt_meta_data_ConfigurationModel,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_ConfigurationModel_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<ConfigurationModel, std::true_type>,
        // method 'valueChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QVariant &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QVariant &, std::false_type>,
        // method 'configItemAdded'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'configItemRemoved'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'configItemUpdated'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'categoryChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<ConfigCategory, std::false_type>,
        // method 'unsavedChangesChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'validationError'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'modelChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'refreshModel'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'applyChanges'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'discardChanges'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'autoSave'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void ConfigurationModel::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ConfigurationModel *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->valueChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariant>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QVariant>>(_a[3]))); break;
        case 1: _t->configItemAdded((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 2: _t->configItemRemoved((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->configItemUpdated((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 4: _t->categoryChanged((*reinterpret_cast< std::add_pointer_t<ConfigCategory>>(_a[1]))); break;
        case 5: _t->unsavedChangesChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 6: _t->validationError((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 7: _t->modelChanged(); break;
        case 8: _t->refreshModel(); break;
        case 9: _t->applyChanges(); break;
        case 10: _t->discardChanges(); break;
        case 11: _t->autoSave(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ConfigurationModel::*)(const QString & , const QVariant & , const QVariant & );
            if (_t _q_method = &ConfigurationModel::valueChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ConfigurationModel::*)(const QString & );
            if (_t _q_method = &ConfigurationModel::configItemAdded; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ConfigurationModel::*)(const QString & );
            if (_t _q_method = &ConfigurationModel::configItemRemoved; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ConfigurationModel::*)(const QString & );
            if (_t _q_method = &ConfigurationModel::configItemUpdated; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (ConfigurationModel::*)(ConfigCategory );
            if (_t _q_method = &ConfigurationModel::categoryChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (ConfigurationModel::*)(bool );
            if (_t _q_method = &ConfigurationModel::unsavedChangesChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (ConfigurationModel::*)(const QString & , const QString & );
            if (_t _q_method = &ConfigurationModel::validationError; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (ConfigurationModel::*)();
            if (_t _q_method = &ConfigurationModel::modelChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 7;
                return;
            }
        }
    }
}

const QMetaObject *ConfigurationModel::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ConfigurationModel::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ConfigurationModel.stringdata0))
        return static_cast<void*>(this);
    return QAbstractItemModel::qt_metacast(_clname);
}

int ConfigurationModel::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QAbstractItemModel::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 12)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 12)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 12;
    }
    return _id;
}

// SIGNAL 0
void ConfigurationModel::valueChanged(const QString & _t1, const QVariant & _t2, const QVariant & _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void ConfigurationModel::configItemAdded(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void ConfigurationModel::configItemRemoved(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void ConfigurationModel::configItemUpdated(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void ConfigurationModel::categoryChanged(ConfigCategory _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void ConfigurationModel::unsavedChangesChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void ConfigurationModel::validationError(const QString & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void ConfigurationModel::modelChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
