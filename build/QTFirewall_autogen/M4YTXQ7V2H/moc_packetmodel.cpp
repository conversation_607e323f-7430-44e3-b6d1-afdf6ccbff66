/****************************************************************************
** Meta object code from reading C++ file 'packetmodel.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.4.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/models/packetmodel.h"
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'packetmodel.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.4.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
namespace {
struct qt_meta_stringdata_PacketModel_t {
    uint offsetsAndSizes[24];
    char stringdata0[12];
    char stringdata1[12];
    char stringdata2[1];
    char stringdata3[11];
    char stringdata4[7];
    char stringdata5[15];
    char stringdata6[18];
    char stringdata7[13];
    char stringdata8[12];
    char stringdata9[17];
    char stringdata10[16];
    char stringdata11[19];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_PacketModel_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_PacketModel_t qt_meta_stringdata_PacketModel = {
    {
        QT_MOC_LITERAL(0, 11),  // "PacketModel"
        QT_MOC_LITERAL(12, 11),  // "packetAdded"
        QT_MOC_LITERAL(24, 0),  // ""
        QT_MOC_LITERAL(25, 10),  // "PacketInfo"
        QT_MOC_LITERAL(36, 6),  // "packet"
        QT_MOC_LITERAL(43, 14),  // "packetsCleared"
        QT_MOC_LITERAL(58, 17),  // "statisticsChanged"
        QT_MOC_LITERAL(76, 12),  // "modelUpdated"
        QT_MOC_LITERAL(89, 11),  // "refreshData"
        QT_MOC_LITERAL(101, 16),  // "startAutoRefresh"
        QT_MOC_LITERAL(118, 15),  // "stopAutoRefresh"
        QT_MOC_LITERAL(134, 18)   // "onAutoRefreshTimer"
    },
    "PacketModel",
    "packetAdded",
    "",
    "PacketInfo",
    "packet",
    "packetsCleared",
    "statisticsChanged",
    "modelUpdated",
    "refreshData",
    "startAutoRefresh",
    "stopAutoRefresh",
    "onAutoRefreshTimer"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_PacketModel[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
       8,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    1,   62,    2, 0x06,    1 /* Public */,
       5,    0,   65,    2, 0x06,    3 /* Public */,
       6,    0,   66,    2, 0x06,    4 /* Public */,
       7,    0,   67,    2, 0x06,    5 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       8,    0,   68,    2, 0x0a,    6 /* Public */,
       9,    0,   69,    2, 0x0a,    7 /* Public */,
      10,    0,   70,    2, 0x0a,    8 /* Public */,
      11,    0,   71,    2, 0x08,    9 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject PacketModel::staticMetaObject = { {
    QMetaObject::SuperData::link<QAbstractTableModel::staticMetaObject>(),
    qt_meta_stringdata_PacketModel.offsetsAndSizes,
    qt_meta_data_PacketModel,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_PacketModel_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<PacketModel, std::true_type>,
        // method 'packetAdded'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const PacketInfo &, std::false_type>,
        // method 'packetsCleared'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'statisticsChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'modelUpdated'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'refreshData'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'startAutoRefresh'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'stopAutoRefresh'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onAutoRefreshTimer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void PacketModel::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<PacketModel *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->packetAdded((*reinterpret_cast< std::add_pointer_t<PacketInfo>>(_a[1]))); break;
        case 1: _t->packetsCleared(); break;
        case 2: _t->statisticsChanged(); break;
        case 3: _t->modelUpdated(); break;
        case 4: _t->refreshData(); break;
        case 5: _t->startAutoRefresh(); break;
        case 6: _t->stopAutoRefresh(); break;
        case 7: _t->onAutoRefreshTimer(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
        case 0:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< PacketInfo >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (PacketModel::*)(const PacketInfo & );
            if (_t _q_method = &PacketModel::packetAdded; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (PacketModel::*)();
            if (_t _q_method = &PacketModel::packetsCleared; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (PacketModel::*)();
            if (_t _q_method = &PacketModel::statisticsChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (PacketModel::*)();
            if (_t _q_method = &PacketModel::modelUpdated; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
    }
}

const QMetaObject *PacketModel::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *PacketModel::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_PacketModel.stringdata0))
        return static_cast<void*>(this);
    return QAbstractTableModel::qt_metacast(_clname);
}

int PacketModel::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QAbstractTableModel::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    }
    return _id;
}

// SIGNAL 0
void PacketModel::packetAdded(const PacketInfo & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void PacketModel::packetsCleared()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void PacketModel::statisticsChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void PacketModel::modelUpdated()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
