/****************************************************************************
** Meta object code from reading C++ file 'rulemodel.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.4.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/models/rulemodel.h"
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'rulemodel.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.4.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
namespace {
struct qt_meta_stringdata_RuleModel_t {
    uint offsetsAndSizes[34];
    char stringdata0[10];
    char stringdata1[10];
    char stringdata2[1];
    char stringdata3[7];
    char stringdata4[12];
    char stringdata5[12];
    char stringdata6[10];
    char stringdata7[9];
    char stringdata8[12];
    char stringdata9[8];
    char stringdata10[18];
    char stringdata11[13];
    char stringdata12[13];
    char stringdata13[15];
    char stringdata14[11];
    char stringdata15[14];
    char stringdata16[14];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_RuleModel_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_RuleModel_t qt_meta_stringdata_RuleModel = {
    {
        QT_MOC_LITERAL(0, 9),  // "RuleModel"
        QT_MOC_LITERAL(10, 9),  // "ruleAdded"
        QT_MOC_LITERAL(20, 0),  // ""
        QT_MOC_LITERAL(21, 6),  // "ruleId"
        QT_MOC_LITERAL(28, 11),  // "ruleRemoved"
        QT_MOC_LITERAL(40, 11),  // "ruleUpdated"
        QT_MOC_LITERAL(52, 9),  // "ruleMoved"
        QT_MOC_LITERAL(62, 8),  // "newIndex"
        QT_MOC_LITERAL(71, 11),  // "ruleEnabled"
        QT_MOC_LITERAL(83, 7),  // "enabled"
        QT_MOC_LITERAL(91, 17),  // "statisticsUpdated"
        QT_MOC_LITERAL(109, 12),  // "modelChanged"
        QT_MOC_LITERAL(122, 12),  // "refreshModel"
        QT_MOC_LITERAL(135, 14),  // "sortByPriority"
        QT_MOC_LITERAL(150, 10),  // "sortByName"
        QT_MOC_LITERAL(161, 13),  // "sortByCreated"
        QT_MOC_LITERAL(175, 13)   // "sortByMatches"
    },
    "RuleModel",
    "ruleAdded",
    "",
    "ruleId",
    "ruleRemoved",
    "ruleUpdated",
    "ruleMoved",
    "newIndex",
    "ruleEnabled",
    "enabled",
    "statisticsUpdated",
    "modelChanged",
    "refreshModel",
    "sortByPriority",
    "sortByName",
    "sortByCreated",
    "sortByMatches"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_RuleModel[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
      12,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       7,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    1,   86,    2, 0x06,    1 /* Public */,
       4,    1,   89,    2, 0x06,    3 /* Public */,
       5,    1,   92,    2, 0x06,    5 /* Public */,
       6,    2,   95,    2, 0x06,    7 /* Public */,
       8,    2,  100,    2, 0x06,   10 /* Public */,
      10,    0,  105,    2, 0x06,   13 /* Public */,
      11,    0,  106,    2, 0x06,   14 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      12,    0,  107,    2, 0x0a,   15 /* Public */,
      13,    0,  108,    2, 0x0a,   16 /* Public */,
      14,    0,  109,    2, 0x0a,   17 /* Public */,
      15,    0,  110,    2, 0x0a,   18 /* Public */,
      16,    0,  111,    2, 0x0a,   19 /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,    3,    7,
    QMetaType::Void, QMetaType::QString, QMetaType::Bool,    3,    9,
    QMetaType::Void,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject RuleModel::staticMetaObject = { {
    QMetaObject::SuperData::link<QAbstractListModel::staticMetaObject>(),
    qt_meta_stringdata_RuleModel.offsetsAndSizes,
    qt_meta_data_RuleModel,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_RuleModel_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<RuleModel, std::true_type>,
        // method 'ruleAdded'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'ruleRemoved'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'ruleUpdated'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'ruleMoved'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'ruleEnabled'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'statisticsUpdated'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'modelChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'refreshModel'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'sortByPriority'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'sortByName'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'sortByCreated'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'sortByMatches'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void RuleModel::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<RuleModel *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->ruleAdded((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 1: _t->ruleRemoved((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 2: _t->ruleUpdated((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->ruleMoved((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 4: _t->ruleEnabled((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 5: _t->statisticsUpdated(); break;
        case 6: _t->modelChanged(); break;
        case 7: _t->refreshModel(); break;
        case 8: _t->sortByPriority(); break;
        case 9: _t->sortByName(); break;
        case 10: _t->sortByCreated(); break;
        case 11: _t->sortByMatches(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (RuleModel::*)(const QString & );
            if (_t _q_method = &RuleModel::ruleAdded; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (RuleModel::*)(const QString & );
            if (_t _q_method = &RuleModel::ruleRemoved; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (RuleModel::*)(const QString & );
            if (_t _q_method = &RuleModel::ruleUpdated; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (RuleModel::*)(const QString & , int );
            if (_t _q_method = &RuleModel::ruleMoved; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (RuleModel::*)(const QString & , bool );
            if (_t _q_method = &RuleModel::ruleEnabled; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (RuleModel::*)();
            if (_t _q_method = &RuleModel::statisticsUpdated; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (RuleModel::*)();
            if (_t _q_method = &RuleModel::modelChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 6;
                return;
            }
        }
    }
}

const QMetaObject *RuleModel::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *RuleModel::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_RuleModel.stringdata0))
        return static_cast<void*>(this);
    return QAbstractListModel::qt_metacast(_clname);
}

int RuleModel::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QAbstractListModel::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 12)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 12)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 12;
    }
    return _id;
}

// SIGNAL 0
void RuleModel::ruleAdded(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void RuleModel::ruleRemoved(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void RuleModel::ruleUpdated(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void RuleModel::ruleMoved(const QString & _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void RuleModel::ruleEnabled(const QString & _t1, bool _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void RuleModel::statisticsUpdated()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void RuleModel::modelChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
