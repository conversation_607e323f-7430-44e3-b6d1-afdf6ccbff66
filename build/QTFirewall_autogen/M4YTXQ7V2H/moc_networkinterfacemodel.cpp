/****************************************************************************
** Meta object code from reading C++ file 'networkinterfacemodel.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.4.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/models/networkinterfacemodel.h"
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'networkinterfacemodel.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.4.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
namespace {
struct qt_meta_stringdata_NetworkInterfaceModel_t {
    uint offsetsAndSizes[46];
    char stringdata0[22];
    char stringdata1[15];
    char stringdata2[1];
    char stringdata3[5];
    char stringdata4[17];
    char stringdata5[17];
    char stringdata6[23];
    char stringdata7[16];
    char stringdata8[7];
    char stringdata9[27];
    char stringdata10[10];
    char stringdata11[26];
    char stringdata12[9];
    char stringdata13[18];
    char stringdata14[21];
    char stringdata15[22];
    char stringdata16[13];
    char stringdata17[16];
    char stringdata18[15];
    char stringdata19[18];
    char stringdata20[20];
    char stringdata21[15];
    char stringdata22[18];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_NetworkInterfaceModel_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_NetworkInterfaceModel_t qt_meta_stringdata_NetworkInterfaceModel = {
    {
        QT_MOC_LITERAL(0, 21),  // "NetworkInterfaceModel"
        QT_MOC_LITERAL(22, 14),  // "interfaceAdded"
        QT_MOC_LITERAL(37, 0),  // ""
        QT_MOC_LITERAL(38, 4),  // "name"
        QT_MOC_LITERAL(43, 16),  // "interfaceRemoved"
        QT_MOC_LITERAL(60, 16),  // "interfaceUpdated"
        QT_MOC_LITERAL(77, 22),  // "interfaceStatusChanged"
        QT_MOC_LITERAL(100, 15),  // "InterfaceStatus"
        QT_MOC_LITERAL(116, 6),  // "status"
        QT_MOC_LITERAL(123, 26),  // "interfaceMonitoringChanged"
        QT_MOC_LITERAL(150, 9),  // "monitored"
        QT_MOC_LITERAL(160, 25),  // "interfaceSelectionChanged"
        QT_MOC_LITERAL(186, 8),  // "selected"
        QT_MOC_LITERAL(195, 17),  // "statisticsUpdated"
        QT_MOC_LITERAL(213, 20),  // "newInterfaceDetected"
        QT_MOC_LITERAL(234, 21),  // "interfaceDisconnected"
        QT_MOC_LITERAL(256, 12),  // "modelChanged"
        QT_MOC_LITERAL(269, 15),  // "startMonitoring"
        QT_MOC_LITERAL(285, 14),  // "stopMonitoring"
        QT_MOC_LITERAL(300, 17),  // "refreshStatistics"
        QT_MOC_LITERAL(318, 19),  // "detectNewInterfaces"
        QT_MOC_LITERAL(338, 14),  // "onRefreshTimer"
        QT_MOC_LITERAL(353, 17)   // "onStatisticsTimer"
    },
    "NetworkInterfaceModel",
    "interfaceAdded",
    "",
    "name",
    "interfaceRemoved",
    "interfaceUpdated",
    "interfaceStatusChanged",
    "InterfaceStatus",
    "status",
    "interfaceMonitoringChanged",
    "monitored",
    "interfaceSelectionChanged",
    "selected",
    "statisticsUpdated",
    "newInterfaceDetected",
    "interfaceDisconnected",
    "modelChanged",
    "startMonitoring",
    "stopMonitoring",
    "refreshStatistics",
    "detectNewInterfaces",
    "onRefreshTimer",
    "onStatisticsTimer"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_NetworkInterfaceModel[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
      16,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
      10,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    1,  110,    2, 0x06,    1 /* Public */,
       4,    1,  113,    2, 0x06,    3 /* Public */,
       5,    1,  116,    2, 0x06,    5 /* Public */,
       6,    2,  119,    2, 0x06,    7 /* Public */,
       9,    2,  124,    2, 0x06,   10 /* Public */,
      11,    2,  129,    2, 0x06,   13 /* Public */,
      13,    1,  134,    2, 0x06,   16 /* Public */,
      14,    1,  137,    2, 0x06,   18 /* Public */,
      15,    1,  140,    2, 0x06,   20 /* Public */,
      16,    0,  143,    2, 0x06,   22 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      17,    0,  144,    2, 0x0a,   23 /* Public */,
      18,    0,  145,    2, 0x0a,   24 /* Public */,
      19,    0,  146,    2, 0x0a,   25 /* Public */,
      20,    0,  147,    2, 0x0a,   26 /* Public */,
      21,    0,  148,    2, 0x08,   27 /* Private */,
      22,    0,  149,    2, 0x08,   28 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 7,    3,    8,
    QMetaType::Void, QMetaType::QString, QMetaType::Bool,    3,   10,
    QMetaType::Void, QMetaType::QString, QMetaType::Bool,    3,   12,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject NetworkInterfaceModel::staticMetaObject = { {
    QMetaObject::SuperData::link<QAbstractListModel::staticMetaObject>(),
    qt_meta_stringdata_NetworkInterfaceModel.offsetsAndSizes,
    qt_meta_data_NetworkInterfaceModel,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_NetworkInterfaceModel_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<NetworkInterfaceModel, std::true_type>,
        // method 'interfaceAdded'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'interfaceRemoved'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'interfaceUpdated'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'interfaceStatusChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<InterfaceStatus, std::false_type>,
        // method 'interfaceMonitoringChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'interfaceSelectionChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'statisticsUpdated'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'newInterfaceDetected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'interfaceDisconnected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'modelChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'startMonitoring'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'stopMonitoring'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'refreshStatistics'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'detectNewInterfaces'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onRefreshTimer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onStatisticsTimer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void NetworkInterfaceModel::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<NetworkInterfaceModel *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->interfaceAdded((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 1: _t->interfaceRemoved((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 2: _t->interfaceUpdated((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->interfaceStatusChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<InterfaceStatus>>(_a[2]))); break;
        case 4: _t->interfaceMonitoringChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 5: _t->interfaceSelectionChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 6: _t->statisticsUpdated((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 7: _t->newInterfaceDetected((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 8: _t->interfaceDisconnected((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 9: _t->modelChanged(); break;
        case 10: _t->startMonitoring(); break;
        case 11: _t->stopMonitoring(); break;
        case 12: _t->refreshStatistics(); break;
        case 13: _t->detectNewInterfaces(); break;
        case 14: _t->onRefreshTimer(); break;
        case 15: _t->onStatisticsTimer(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (NetworkInterfaceModel::*)(const QString & );
            if (_t _q_method = &NetworkInterfaceModel::interfaceAdded; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (NetworkInterfaceModel::*)(const QString & );
            if (_t _q_method = &NetworkInterfaceModel::interfaceRemoved; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (NetworkInterfaceModel::*)(const QString & );
            if (_t _q_method = &NetworkInterfaceModel::interfaceUpdated; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (NetworkInterfaceModel::*)(const QString & , InterfaceStatus );
            if (_t _q_method = &NetworkInterfaceModel::interfaceStatusChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (NetworkInterfaceModel::*)(const QString & , bool );
            if (_t _q_method = &NetworkInterfaceModel::interfaceMonitoringChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (NetworkInterfaceModel::*)(const QString & , bool );
            if (_t _q_method = &NetworkInterfaceModel::interfaceSelectionChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (NetworkInterfaceModel::*)(const QString & );
            if (_t _q_method = &NetworkInterfaceModel::statisticsUpdated; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (NetworkInterfaceModel::*)(const QString & );
            if (_t _q_method = &NetworkInterfaceModel::newInterfaceDetected; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 7;
                return;
            }
        }
        {
            using _t = void (NetworkInterfaceModel::*)(const QString & );
            if (_t _q_method = &NetworkInterfaceModel::interfaceDisconnected; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 8;
                return;
            }
        }
        {
            using _t = void (NetworkInterfaceModel::*)();
            if (_t _q_method = &NetworkInterfaceModel::modelChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 9;
                return;
            }
        }
    }
}

const QMetaObject *NetworkInterfaceModel::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *NetworkInterfaceModel::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_NetworkInterfaceModel.stringdata0))
        return static_cast<void*>(this);
    return QAbstractListModel::qt_metacast(_clname);
}

int NetworkInterfaceModel::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QAbstractListModel::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 16)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 16;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 16)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 16;
    }
    return _id;
}

// SIGNAL 0
void NetworkInterfaceModel::interfaceAdded(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void NetworkInterfaceModel::interfaceRemoved(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void NetworkInterfaceModel::interfaceUpdated(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void NetworkInterfaceModel::interfaceStatusChanged(const QString & _t1, InterfaceStatus _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void NetworkInterfaceModel::interfaceMonitoringChanged(const QString & _t1, bool _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void NetworkInterfaceModel::interfaceSelectionChanged(const QString & _t1, bool _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void NetworkInterfaceModel::statisticsUpdated(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void NetworkInterfaceModel::newInterfaceDetected(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 7, _a);
}

// SIGNAL 8
void NetworkInterfaceModel::interfaceDisconnected(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 8, _a);
}

// SIGNAL 9
void NetworkInterfaceModel::modelChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 9, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
