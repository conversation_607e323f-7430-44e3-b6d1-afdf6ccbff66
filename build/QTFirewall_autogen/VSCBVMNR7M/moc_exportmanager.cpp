/****************************************************************************
** Meta object code from reading C++ file 'exportmanager.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.4.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/utils/exportmanager.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'exportmanager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.4.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
namespace {
struct qt_meta_stringdata_ExportManager_t {
    uint offsetsAndSizes[56];
    char stringdata0[14];
    char stringdata1[14];
    char stringdata2[1];
    char stringdata3[9];
    char stringdata4[15];
    char stringdata5[11];
    char stringdata6[20];
    char stringdata7[13];
    char stringdata8[7];
    char stringdata9[16];
    char stringdata10[16];
    char stringdata11[12];
    char stringdata12[6];
    char stringdata13[20];
    char stringdata14[8];
    char stringdata15[6];
    char stringdata16[21];
    char stringdata17[14];
    char stringdata18[13];
    char stringdata19[7];
    char stringdata20[12];
    char stringdata21[13];
    char stringdata22[17];
    char stringdata23[20];
    char stringdata24[10];
    char stringdata25[9];
    char stringdata26[23];
    char stringdata27[17];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_ExportManager_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_ExportManager_t qt_meta_stringdata_ExportManager = {
    {
        QT_MOC_LITERAL(0, 13),  // "ExportManager"
        QT_MOC_LITERAL(14, 13),  // "exportStarted"
        QT_MOC_LITERAL(28, 0),  // ""
        QT_MOC_LITERAL(29, 8),  // "filename"
        QT_MOC_LITERAL(38, 14),  // "exportProgress"
        QT_MOC_LITERAL(53, 10),  // "percentage"
        QT_MOC_LITERAL(64, 19),  // "exportStatusChanged"
        QT_MOC_LITERAL(84, 12),  // "ExportStatus"
        QT_MOC_LITERAL(97, 6),  // "status"
        QT_MOC_LITERAL(104, 15),  // "exportCompleted"
        QT_MOC_LITERAL(120, 15),  // "exportCancelled"
        QT_MOC_LITERAL(136, 11),  // "exportError"
        QT_MOC_LITERAL(148, 5),  // "error"
        QT_MOC_LITERAL(154, 19),  // "batchExportProgress"
        QT_MOC_LITERAL(174, 7),  // "current"
        QT_MOC_LITERAL(182, 5),  // "total"
        QT_MOC_LITERAL(188, 20),  // "batchExportCompleted"
        QT_MOC_LITERAL(209, 13),  // "exportPackets"
        QT_MOC_LITERAL(223, 12),  // "ExportFormat"
        QT_MOC_LITERAL(236, 6),  // "format"
        QT_MOC_LITERAL(243, 11),  // "exportRules"
        QT_MOC_LITERAL(255, 12),  // "exportAlerts"
        QT_MOC_LITERAL(268, 16),  // "exportStatistics"
        QT_MOC_LITERAL(285, 19),  // "exportConfiguration"
        QT_MOC_LITERAL(305, 9),  // "exportAll"
        QT_MOC_LITERAL(315, 8),  // "basePath"
        QT_MOC_LITERAL(324, 22),  // "onExportThreadFinished"
        QT_MOC_LITERAL(347, 16)   // "onProgressUpdate"
    },
    "ExportManager",
    "exportStarted",
    "",
    "filename",
    "exportProgress",
    "percentage",
    "exportStatusChanged",
    "ExportStatus",
    "status",
    "exportCompleted",
    "exportCancelled",
    "exportError",
    "error",
    "batchExportProgress",
    "current",
    "total",
    "batchExportCompleted",
    "exportPackets",
    "ExportFormat",
    "format",
    "exportRules",
    "exportAlerts",
    "exportStatistics",
    "exportConfiguration",
    "exportAll",
    "basePath",
    "onExportThreadFinished",
    "onProgressUpdate"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_ExportManager[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
      22,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       8,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    1,  146,    2, 0x06,    1 /* Public */,
       4,    1,  149,    2, 0x06,    3 /* Public */,
       6,    1,  152,    2, 0x06,    5 /* Public */,
       9,    1,  155,    2, 0x06,    7 /* Public */,
      10,    0,  158,    2, 0x06,    9 /* Public */,
      11,    1,  159,    2, 0x06,   10 /* Public */,
      13,    2,  162,    2, 0x06,   12 /* Public */,
      16,    0,  167,    2, 0x06,   15 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      17,    2,  168,    2, 0x0a,   16 /* Public */,
      17,    1,  173,    2, 0x2a,   19 /* Public | MethodCloned */,
      20,    2,  176,    2, 0x0a,   21 /* Public */,
      20,    1,  181,    2, 0x2a,   24 /* Public | MethodCloned */,
      21,    2,  184,    2, 0x0a,   26 /* Public */,
      21,    1,  189,    2, 0x2a,   29 /* Public | MethodCloned */,
      22,    2,  192,    2, 0x0a,   31 /* Public */,
      22,    1,  197,    2, 0x2a,   34 /* Public | MethodCloned */,
      23,    2,  200,    2, 0x0a,   36 /* Public */,
      23,    1,  205,    2, 0x2a,   39 /* Public | MethodCloned */,
      24,    2,  208,    2, 0x0a,   41 /* Public */,
      24,    1,  213,    2, 0x2a,   44 /* Public | MethodCloned */,
      26,    0,  216,    2, 0x08,   46 /* Private */,
      27,    1,  217,    2, 0x08,   47 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::Int,    5,
    QMetaType::Void, 0x80000000 | 7,    8,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   12,
    QMetaType::Void, QMetaType::Int, QMetaType::Int,   14,   15,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void, QMetaType::QString, 0x80000000 | 18,    3,   19,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 18,    3,   19,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 18,    3,   19,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 18,    3,   19,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 18,    3,   19,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 18,   25,   19,
    QMetaType::Void, QMetaType::QString,   25,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,    5,

       0        // eod
};

Q_CONSTINIT const QMetaObject ExportManager::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_ExportManager.offsetsAndSizes,
    qt_meta_data_ExportManager,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_ExportManager_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<ExportManager, std::true_type>,
        // method 'exportStarted'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'exportProgress'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'exportStatusChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<ExportStatus, std::false_type>,
        // method 'exportCompleted'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'exportCancelled'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'exportError'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'batchExportProgress'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'batchExportCompleted'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'exportPackets'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<ExportFormat, std::false_type>,
        // method 'exportPackets'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'exportRules'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<ExportFormat, std::false_type>,
        // method 'exportRules'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'exportAlerts'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<ExportFormat, std::false_type>,
        // method 'exportAlerts'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'exportStatistics'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<ExportFormat, std::false_type>,
        // method 'exportStatistics'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'exportConfiguration'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<ExportFormat, std::false_type>,
        // method 'exportConfiguration'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'exportAll'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<ExportFormat, std::false_type>,
        // method 'exportAll'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'onExportThreadFinished'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onProgressUpdate'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>
    >,
    nullptr
} };

void ExportManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ExportManager *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->exportStarted((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 1: _t->exportProgress((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 2: _t->exportStatusChanged((*reinterpret_cast< std::add_pointer_t<ExportStatus>>(_a[1]))); break;
        case 3: _t->exportCompleted((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 4: _t->exportCancelled(); break;
        case 5: _t->exportError((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 6: _t->batchExportProgress((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 7: _t->batchExportCompleted(); break;
        case 8: _t->exportPackets((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<ExportFormat>>(_a[2]))); break;
        case 9: _t->exportPackets((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 10: _t->exportRules((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<ExportFormat>>(_a[2]))); break;
        case 11: _t->exportRules((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 12: _t->exportAlerts((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<ExportFormat>>(_a[2]))); break;
        case 13: _t->exportAlerts((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 14: _t->exportStatistics((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<ExportFormat>>(_a[2]))); break;
        case 15: _t->exportStatistics((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 16: _t->exportConfiguration((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<ExportFormat>>(_a[2]))); break;
        case 17: _t->exportConfiguration((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 18: _t->exportAll((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<ExportFormat>>(_a[2]))); break;
        case 19: _t->exportAll((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 20: _t->onExportThreadFinished(); break;
        case 21: _t->onProgressUpdate((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ExportManager::*)(const QString & );
            if (_t _q_method = &ExportManager::exportStarted; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ExportManager::*)(int );
            if (_t _q_method = &ExportManager::exportProgress; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ExportManager::*)(ExportStatus );
            if (_t _q_method = &ExportManager::exportStatusChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ExportManager::*)(const QString & );
            if (_t _q_method = &ExportManager::exportCompleted; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (ExportManager::*)();
            if (_t _q_method = &ExportManager::exportCancelled; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (ExportManager::*)(const QString & );
            if (_t _q_method = &ExportManager::exportError; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (ExportManager::*)(int , int );
            if (_t _q_method = &ExportManager::batchExportProgress; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (ExportManager::*)();
            if (_t _q_method = &ExportManager::batchExportCompleted; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 7;
                return;
            }
        }
    }
}

const QMetaObject *ExportManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ExportManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ExportManager.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ExportManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 22)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 22;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 22)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 22;
    }
    return _id;
}

// SIGNAL 0
void ExportManager::exportStarted(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void ExportManager::exportProgress(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void ExportManager::exportStatusChanged(ExportStatus _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void ExportManager::exportCompleted(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void ExportManager::exportCancelled()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ExportManager::exportError(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void ExportManager::batchExportProgress(int _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void ExportManager::batchExportCompleted()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}
namespace {
struct qt_meta_stringdata_ExportWorker_t {
    uint offsetsAndSizes[20];
    char stringdata0[13];
    char stringdata1[16];
    char stringdata2[1];
    char stringdata3[11];
    char stringdata4[17];
    char stringdata5[10];
    char stringdata6[16];
    char stringdata7[13];
    char stringdata8[6];
    char stringdata9[14];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_ExportWorker_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_ExportWorker_t qt_meta_stringdata_ExportWorker = {
    {
        QT_MOC_LITERAL(0, 12),  // "ExportWorker"
        QT_MOC_LITERAL(13, 15),  // "progressUpdated"
        QT_MOC_LITERAL(29, 0),  // ""
        QT_MOC_LITERAL(30, 10),  // "percentage"
        QT_MOC_LITERAL(41, 16),  // "operationChanged"
        QT_MOC_LITERAL(58, 9),  // "operation"
        QT_MOC_LITERAL(68, 15),  // "exportCompleted"
        QT_MOC_LITERAL(84, 12),  // "exportFailed"
        QT_MOC_LITERAL(97, 5),  // "error"
        QT_MOC_LITERAL(103, 13)   // "performExport"
    },
    "ExportWorker",
    "progressUpdated",
    "",
    "percentage",
    "operationChanged",
    "operation",
    "exportCompleted",
    "exportFailed",
    "error",
    "performExport"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_ExportWorker[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
       5,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    1,   44,    2, 0x06,    1 /* Public */,
       4,    1,   47,    2, 0x06,    3 /* Public */,
       6,    0,   50,    2, 0x06,    5 /* Public */,
       7,    1,   51,    2, 0x06,    6 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       9,    0,   54,    2, 0x0a,    8 /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::Int,    3,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    8,

 // slots: parameters
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject ExportWorker::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_ExportWorker.offsetsAndSizes,
    qt_meta_data_ExportWorker,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_ExportWorker_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<ExportWorker, std::true_type>,
        // method 'progressUpdated'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'operationChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'exportCompleted'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'exportFailed'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'performExport'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void ExportWorker::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ExportWorker *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->progressUpdated((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 1: _t->operationChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 2: _t->exportCompleted(); break;
        case 3: _t->exportFailed((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 4: _t->performExport(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ExportWorker::*)(int );
            if (_t _q_method = &ExportWorker::progressUpdated; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ExportWorker::*)(const QString & );
            if (_t _q_method = &ExportWorker::operationChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ExportWorker::*)();
            if (_t _q_method = &ExportWorker::exportCompleted; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ExportWorker::*)(const QString & );
            if (_t _q_method = &ExportWorker::exportFailed; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
    }
}

const QMetaObject *ExportWorker::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ExportWorker::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ExportWorker.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ExportWorker::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 5)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 5;
    }
    return _id;
}

// SIGNAL 0
void ExportWorker::progressUpdated(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void ExportWorker::operationChanged(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void ExportWorker::exportCompleted()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ExportWorker::exportFailed(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
