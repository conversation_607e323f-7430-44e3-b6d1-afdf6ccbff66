/****************************************************************************
** Meta object code from reading C++ file 'animationhelper.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.4.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/utils/animationhelper.h"
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'animationhelper.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.4.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
namespace {
struct qt_meta_stringdata_AnimationHelper_t {
    uint offsetsAndSizes[26];
    char stringdata0[16];
    char stringdata1[17];
    char stringdata2[1];
    char stringdata3[20];
    char stringdata4[10];
    char stringdata5[18];
    char stringdata6[16];
    char stringdata7[17];
    char stringdata8[20];
    char stringdata9[24];
    char stringdata10[26];
    char stringdata11[9];
    char stringdata12[9];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_AnimationHelper_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_AnimationHelper_t qt_meta_stringdata_AnimationHelper = {
    {
        QT_MOC_LITERAL(0, 15),  // "AnimationHelper"
        QT_MOC_LITERAL(16, 16),  // "animationStarted"
        QT_MOC_LITERAL(33, 0),  // ""
        QT_MOC_LITERAL(34, 19),  // "QAbstractAnimation*"
        QT_MOC_LITERAL(54, 9),  // "animation"
        QT_MOC_LITERAL(64, 17),  // "animationFinished"
        QT_MOC_LITERAL(82, 15),  // "animationPaused"
        QT_MOC_LITERAL(98, 16),  // "animationResumed"
        QT_MOC_LITERAL(115, 19),  // "onAnimationFinished"
        QT_MOC_LITERAL(135, 23),  // "onAnimationStateChanged"
        QT_MOC_LITERAL(159, 25),  // "QAbstractAnimation::State"
        QT_MOC_LITERAL(185, 8),  // "newState"
        QT_MOC_LITERAL(194, 8)   // "oldState"
    },
    "AnimationHelper",
    "animationStarted",
    "",
    "QAbstractAnimation*",
    "animation",
    "animationFinished",
    "animationPaused",
    "animationResumed",
    "onAnimationFinished",
    "onAnimationStateChanged",
    "QAbstractAnimation::State",
    "newState",
    "oldState"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_AnimationHelper[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
       6,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    1,   50,    2, 0x06,    1 /* Public */,
       5,    1,   53,    2, 0x06,    3 /* Public */,
       6,    1,   56,    2, 0x06,    5 /* Public */,
       7,    1,   59,    2, 0x06,    7 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       8,    0,   62,    2, 0x0a,    9 /* Public */,
       9,    2,   63,    2, 0x0a,   10 /* Public */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 3,    4,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 10, 0x80000000 | 10,   11,   12,

       0        // eod
};

Q_CONSTINIT const QMetaObject AnimationHelper::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_AnimationHelper.offsetsAndSizes,
    qt_meta_data_AnimationHelper,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_AnimationHelper_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<AnimationHelper, std::true_type>,
        // method 'animationStarted'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QAbstractAnimation *, std::false_type>,
        // method 'animationFinished'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QAbstractAnimation *, std::false_type>,
        // method 'animationPaused'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QAbstractAnimation *, std::false_type>,
        // method 'animationResumed'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QAbstractAnimation *, std::false_type>,
        // method 'onAnimationFinished'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onAnimationStateChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QAbstractAnimation::State, std::false_type>,
        QtPrivate::TypeAndForceComplete<QAbstractAnimation::State, std::false_type>
    >,
    nullptr
} };

void AnimationHelper::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<AnimationHelper *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->animationStarted((*reinterpret_cast< std::add_pointer_t<QAbstractAnimation*>>(_a[1]))); break;
        case 1: _t->animationFinished((*reinterpret_cast< std::add_pointer_t<QAbstractAnimation*>>(_a[1]))); break;
        case 2: _t->animationPaused((*reinterpret_cast< std::add_pointer_t<QAbstractAnimation*>>(_a[1]))); break;
        case 3: _t->animationResumed((*reinterpret_cast< std::add_pointer_t<QAbstractAnimation*>>(_a[1]))); break;
        case 4: _t->onAnimationFinished(); break;
        case 5: _t->onAnimationStateChanged((*reinterpret_cast< std::add_pointer_t<QAbstractAnimation::State>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QAbstractAnimation::State>>(_a[2]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
        case 0:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< QAbstractAnimation* >(); break;
            }
            break;
        case 1:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< QAbstractAnimation* >(); break;
            }
            break;
        case 2:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< QAbstractAnimation* >(); break;
            }
            break;
        case 3:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< QAbstractAnimation* >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (AnimationHelper::*)(QAbstractAnimation * );
            if (_t _q_method = &AnimationHelper::animationStarted; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (AnimationHelper::*)(QAbstractAnimation * );
            if (_t _q_method = &AnimationHelper::animationFinished; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (AnimationHelper::*)(QAbstractAnimation * );
            if (_t _q_method = &AnimationHelper::animationPaused; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (AnimationHelper::*)(QAbstractAnimation * );
            if (_t _q_method = &AnimationHelper::animationResumed; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
    }
}

const QMetaObject *AnimationHelper::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AnimationHelper::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_AnimationHelper.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int AnimationHelper::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    }
    return _id;
}

// SIGNAL 0
void AnimationHelper::animationStarted(QAbstractAnimation * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void AnimationHelper::animationFinished(QAbstractAnimation * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void AnimationHelper::animationPaused(QAbstractAnimation * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void AnimationHelper::animationResumed(QAbstractAnimation * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
