/****************************************************************************
** Meta object code from reading C++ file 'ruledialog.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.4.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/ui/dialogs/ruledialog.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ruledialog.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.4.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
namespace {
struct qt_meta_stringdata_RuleDialog_t {
    uint offsetsAndSizes[56];
    char stringdata0[11];
    char stringdata1[12];
    char stringdata2[1];
    char stringdata3[13];
    char stringdata4[5];
    char stringdata5[12];
    char stringdata6[11];
    char stringdata7[7];
    char stringdata8[14];
    char stringdata9[13];
    char stringdata10[7];
    char stringdata11[7];
    char stringdata12[16];
    char stringdata13[13];
    char stringdata14[15];
    char stringdata15[9];
    char stringdata16[12];
    char stringdata17[19];
    char stringdata18[20];
    char stringdata19[17];
    char stringdata20[25];
    char stringdata21[13];
    char stringdata22[6];
    char stringdata23[18];
    char stringdata24[15];
    char stringdata25[16];
    char stringdata26[13];
    char stringdata27[13];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_RuleDialog_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_RuleDialog_t qt_meta_stringdata_RuleDialog = {
    {
        QT_MOC_LITERAL(0, 10),  // "RuleDialog"
        QT_MOC_LITERAL(11, 11),  // "ruleCreated"
        QT_MOC_LITERAL(23, 0),  // ""
        QT_MOC_LITERAL(24, 12),  // "FirewallRule"
        QT_MOC_LITERAL(37, 4),  // "rule"
        QT_MOC_LITERAL(42, 11),  // "ruleUpdated"
        QT_MOC_LITERAL(54, 10),  // "ruleTested"
        QT_MOC_LITERAL(65, 6),  // "passed"
        QT_MOC_LITERAL(72, 13),  // "templateSaved"
        QT_MOC_LITERAL(86, 12),  // "templateName"
        QT_MOC_LITERAL(99, 6),  // "accept"
        QT_MOC_LITERAL(106, 6),  // "reject"
        QT_MOC_LITERAL(113, 15),  // "resetToDefaults"
        QT_MOC_LITERAL(129, 12),  // "loadTemplate"
        QT_MOC_LITERAL(142, 14),  // "saveAsTemplate"
        QT_MOC_LITERAL(157, 8),  // "testRule"
        QT_MOC_LITERAL(166, 11),  // "previewRule"
        QT_MOC_LITERAL(178, 18),  // "onBasicInfoChanged"
        QT_MOC_LITERAL(197, 19),  // "onConditionsChanged"
        QT_MOC_LITERAL(217, 16),  // "onActionsChanged"
        QT_MOC_LITERAL(234, 24),  // "onAdvancedOptionsChanged"
        QT_MOC_LITERAL(259, 12),  // "onTabChanged"
        QT_MOC_LITERAL(272, 5),  // "index"
        QT_MOC_LITERAL(278, 17),  // "onValidationTimer"
        QT_MOC_LITERAL(296, 14),  // "onPreviewTimer"
        QT_MOC_LITERAL(311, 15),  // "onHelpRequested"
        QT_MOC_LITERAL(327, 12),  // "onImportRule"
        QT_MOC_LITERAL(340, 12)   // "onExportRule"
    },
    "RuleDialog",
    "ruleCreated",
    "",
    "FirewallRule",
    "rule",
    "ruleUpdated",
    "ruleTested",
    "passed",
    "templateSaved",
    "templateName",
    "accept",
    "reject",
    "resetToDefaults",
    "loadTemplate",
    "saveAsTemplate",
    "testRule",
    "previewRule",
    "onBasicInfoChanged",
    "onConditionsChanged",
    "onActionsChanged",
    "onAdvancedOptionsChanged",
    "onTabChanged",
    "index",
    "onValidationTimer",
    "onPreviewTimer",
    "onHelpRequested",
    "onImportRule",
    "onExportRule"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_RuleDialog[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
      21,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    1,  140,    2, 0x06,    1 /* Public */,
       5,    1,  143,    2, 0x06,    3 /* Public */,
       6,    2,  146,    2, 0x06,    5 /* Public */,
       8,    1,  151,    2, 0x06,    8 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      10,    0,  154,    2, 0x0a,   10 /* Public */,
      11,    0,  155,    2, 0x0a,   11 /* Public */,
      12,    0,  156,    2, 0x0a,   12 /* Public */,
      13,    1,  157,    2, 0x0a,   13 /* Public */,
      14,    0,  160,    2, 0x0a,   15 /* Public */,
      15,    0,  161,    2, 0x0a,   16 /* Public */,
      16,    0,  162,    2, 0x0a,   17 /* Public */,
      17,    0,  163,    2, 0x08,   18 /* Private */,
      18,    0,  164,    2, 0x08,   19 /* Private */,
      19,    0,  165,    2, 0x08,   20 /* Private */,
      20,    0,  166,    2, 0x08,   21 /* Private */,
      21,    1,  167,    2, 0x08,   22 /* Private */,
      23,    0,  170,    2, 0x08,   24 /* Private */,
      24,    0,  171,    2, 0x08,   25 /* Private */,
      25,    0,  172,    2, 0x08,   26 /* Private */,
      26,    0,  173,    2, 0x08,   27 /* Private */,
      27,    0,  174,    2, 0x08,   28 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 3, QMetaType::Bool,    4,    7,
    QMetaType::Void, QMetaType::QString,    9,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    9,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   22,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject RuleDialog::staticMetaObject = { {
    QMetaObject::SuperData::link<QDialog::staticMetaObject>(),
    qt_meta_stringdata_RuleDialog.offsetsAndSizes,
    qt_meta_data_RuleDialog,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_RuleDialog_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<RuleDialog, std::true_type>,
        // method 'ruleCreated'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const FirewallRule &, std::false_type>,
        // method 'ruleUpdated'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const FirewallRule &, std::false_type>,
        // method 'ruleTested'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const FirewallRule &, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'templateSaved'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'accept'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'reject'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'resetToDefaults'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'loadTemplate'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'saveAsTemplate'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'testRule'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'previewRule'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onBasicInfoChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onConditionsChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onActionsChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onAdvancedOptionsChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onTabChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'onValidationTimer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onPreviewTimer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onHelpRequested'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onImportRule'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onExportRule'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void RuleDialog::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<RuleDialog *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->ruleCreated((*reinterpret_cast< std::add_pointer_t<FirewallRule>>(_a[1]))); break;
        case 1: _t->ruleUpdated((*reinterpret_cast< std::add_pointer_t<FirewallRule>>(_a[1]))); break;
        case 2: _t->ruleTested((*reinterpret_cast< std::add_pointer_t<FirewallRule>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 3: _t->templateSaved((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 4: _t->accept(); break;
        case 5: _t->reject(); break;
        case 6: _t->resetToDefaults(); break;
        case 7: _t->loadTemplate((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 8: _t->saveAsTemplate(); break;
        case 9: _t->testRule(); break;
        case 10: _t->previewRule(); break;
        case 11: _t->onBasicInfoChanged(); break;
        case 12: _t->onConditionsChanged(); break;
        case 13: _t->onActionsChanged(); break;
        case 14: _t->onAdvancedOptionsChanged(); break;
        case 15: _t->onTabChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 16: _t->onValidationTimer(); break;
        case 17: _t->onPreviewTimer(); break;
        case 18: _t->onHelpRequested(); break;
        case 19: _t->onImportRule(); break;
        case 20: _t->onExportRule(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (RuleDialog::*)(const FirewallRule & );
            if (_t _q_method = &RuleDialog::ruleCreated; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (RuleDialog::*)(const FirewallRule & );
            if (_t _q_method = &RuleDialog::ruleUpdated; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (RuleDialog::*)(const FirewallRule & , bool );
            if (_t _q_method = &RuleDialog::ruleTested; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (RuleDialog::*)(const QString & );
            if (_t _q_method = &RuleDialog::templateSaved; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
    }
}

const QMetaObject *RuleDialog::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *RuleDialog::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_RuleDialog.stringdata0))
        return static_cast<void*>(this);
    return QDialog::qt_metacast(_clname);
}

int RuleDialog::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QDialog::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 21)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 21;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 21)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 21;
    }
    return _id;
}

// SIGNAL 0
void RuleDialog::ruleCreated(const FirewallRule & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void RuleDialog::ruleUpdated(const FirewallRule & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void RuleDialog::ruleTested(const FirewallRule & _t1, bool _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void RuleDialog::templateSaved(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
