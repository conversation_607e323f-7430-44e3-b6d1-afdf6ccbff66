/****************************************************************************
** Meta object code from reading C++ file 'exportdialog.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.4.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/ui/dialogs/exportdialog.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'exportdialog.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.4.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
namespace {
struct qt_meta_stringdata_ExportDialog_t {
    uint offsetsAndSizes[60];
    char stringdata0[13];
    char stringdata1[14];
    char stringdata2[1];
    char stringdata3[15];
    char stringdata4[11];
    char stringdata5[16];
    char stringdata6[9];
    char stringdata7[16];
    char stringdata8[12];
    char stringdata9[6];
    char stringdata10[13];
    char stringdata11[8];
    char stringdata12[7];
    char stringdata13[7];
    char stringdata14[12];
    char stringdata15[13];
    char stringdata16[14];
    char stringdata17[17];
    char stringdata18[15];
    char stringdata19[13];
    char stringdata20[16];
    char stringdata21[18];
    char stringdata22[17];
    char stringdata23[24];
    char stringdata24[19];
    char stringdata25[18];
    char stringdata26[17];
    char stringdata27[6];
    char stringdata28[17];
    char stringdata29[15];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_ExportDialog_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_ExportDialog_t qt_meta_stringdata_ExportDialog = {
    {
        QT_MOC_LITERAL(0, 12),  // "ExportDialog"
        QT_MOC_LITERAL(13, 13),  // "exportStarted"
        QT_MOC_LITERAL(27, 0),  // ""
        QT_MOC_LITERAL(28, 14),  // "exportProgress"
        QT_MOC_LITERAL(43, 10),  // "percentage"
        QT_MOC_LITERAL(54, 15),  // "exportCompleted"
        QT_MOC_LITERAL(70, 8),  // "filename"
        QT_MOC_LITERAL(79, 15),  // "exportCancelled"
        QT_MOC_LITERAL(95, 11),  // "exportError"
        QT_MOC_LITERAL(107, 5),  // "error"
        QT_MOC_LITERAL(113, 12),  // "previewReady"
        QT_MOC_LITERAL(126, 7),  // "preview"
        QT_MOC_LITERAL(134, 6),  // "accept"
        QT_MOC_LITERAL(141, 6),  // "reject"
        QT_MOC_LITERAL(148, 11),  // "startExport"
        QT_MOC_LITERAL(160, 12),  // "cancelExport"
        QT_MOC_LITERAL(173, 13),  // "previewExport"
        QT_MOC_LITERAL(187, 16),  // "selectOutputFile"
        QT_MOC_LITERAL(204, 14),  // "selectTemplate"
        QT_MOC_LITERAL(219, 12),  // "saveTemplate"
        QT_MOC_LITERAL(232, 15),  // "onFormatChanged"
        QT_MOC_LITERAL(248, 17),  // "onDataTypeChanged"
        QT_MOC_LITERAL(266, 16),  // "onOptionsChanged"
        QT_MOC_LITERAL(283, 23),  // "onFieldSelectionChanged"
        QT_MOC_LITERAL(307, 18),  // "onDateRangeChanged"
        QT_MOC_LITERAL(326, 17),  // "onTemplateChanged"
        QT_MOC_LITERAL(344, 16),  // "onExportProgress"
        QT_MOC_LITERAL(361, 5),  // "value"
        QT_MOC_LITERAL(367, 16),  // "onExportFinished"
        QT_MOC_LITERAL(384, 14)   // "onPreviewTimer"
    },
    "ExportDialog",
    "exportStarted",
    "",
    "exportProgress",
    "percentage",
    "exportCompleted",
    "filename",
    "exportCancelled",
    "exportError",
    "error",
    "previewReady",
    "preview",
    "accept",
    "reject",
    "startExport",
    "cancelExport",
    "previewExport",
    "selectOutputFile",
    "selectTemplate",
    "saveTemplate",
    "onFormatChanged",
    "onDataTypeChanged",
    "onOptionsChanged",
    "onFieldSelectionChanged",
    "onDateRangeChanged",
    "onTemplateChanged",
    "onExportProgress",
    "value",
    "onExportFinished",
    "onPreviewTimer"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_ExportDialog[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
      23,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       6,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,  152,    2, 0x06,    1 /* Public */,
       3,    1,  153,    2, 0x06,    2 /* Public */,
       5,    1,  156,    2, 0x06,    4 /* Public */,
       7,    0,  159,    2, 0x06,    6 /* Public */,
       8,    1,  160,    2, 0x06,    7 /* Public */,
      10,    1,  163,    2, 0x06,    9 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      12,    0,  166,    2, 0x0a,   11 /* Public */,
      13,    0,  167,    2, 0x0a,   12 /* Public */,
      14,    0,  168,    2, 0x0a,   13 /* Public */,
      15,    0,  169,    2, 0x0a,   14 /* Public */,
      16,    0,  170,    2, 0x0a,   15 /* Public */,
      17,    0,  171,    2, 0x0a,   16 /* Public */,
      18,    0,  172,    2, 0x0a,   17 /* Public */,
      19,    0,  173,    2, 0x0a,   18 /* Public */,
      20,    0,  174,    2, 0x08,   19 /* Private */,
      21,    0,  175,    2, 0x08,   20 /* Private */,
      22,    0,  176,    2, 0x08,   21 /* Private */,
      23,    0,  177,    2, 0x08,   22 /* Private */,
      24,    0,  178,    2, 0x08,   23 /* Private */,
      25,    0,  179,    2, 0x08,   24 /* Private */,
      26,    1,  180,    2, 0x08,   25 /* Private */,
      28,    0,  183,    2, 0x08,   27 /* Private */,
      29,    0,  184,    2, 0x08,   28 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,    4,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    9,
    QMetaType::Void, QMetaType::QString,   11,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   27,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject ExportDialog::staticMetaObject = { {
    QMetaObject::SuperData::link<QDialog::staticMetaObject>(),
    qt_meta_stringdata_ExportDialog.offsetsAndSizes,
    qt_meta_data_ExportDialog,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_ExportDialog_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<ExportDialog, std::true_type>,
        // method 'exportStarted'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'exportProgress'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'exportCompleted'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'exportCancelled'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'exportError'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'previewReady'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'accept'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'reject'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'startExport'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'cancelExport'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'previewExport'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'selectOutputFile'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'selectTemplate'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'saveTemplate'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onFormatChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onDataTypeChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onOptionsChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onFieldSelectionChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onDateRangeChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onTemplateChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onExportProgress'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'onExportFinished'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onPreviewTimer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void ExportDialog::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ExportDialog *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->exportStarted(); break;
        case 1: _t->exportProgress((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 2: _t->exportCompleted((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->exportCancelled(); break;
        case 4: _t->exportError((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 5: _t->previewReady((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 6: _t->accept(); break;
        case 7: _t->reject(); break;
        case 8: _t->startExport(); break;
        case 9: _t->cancelExport(); break;
        case 10: _t->previewExport(); break;
        case 11: _t->selectOutputFile(); break;
        case 12: _t->selectTemplate(); break;
        case 13: _t->saveTemplate(); break;
        case 14: _t->onFormatChanged(); break;
        case 15: _t->onDataTypeChanged(); break;
        case 16: _t->onOptionsChanged(); break;
        case 17: _t->onFieldSelectionChanged(); break;
        case 18: _t->onDateRangeChanged(); break;
        case 19: _t->onTemplateChanged(); break;
        case 20: _t->onExportProgress((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 21: _t->onExportFinished(); break;
        case 22: _t->onPreviewTimer(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ExportDialog::*)();
            if (_t _q_method = &ExportDialog::exportStarted; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ExportDialog::*)(int );
            if (_t _q_method = &ExportDialog::exportProgress; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ExportDialog::*)(const QString & );
            if (_t _q_method = &ExportDialog::exportCompleted; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ExportDialog::*)();
            if (_t _q_method = &ExportDialog::exportCancelled; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (ExportDialog::*)(const QString & );
            if (_t _q_method = &ExportDialog::exportError; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (ExportDialog::*)(const QString & );
            if (_t _q_method = &ExportDialog::previewReady; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 5;
                return;
            }
        }
    }
}

const QMetaObject *ExportDialog::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ExportDialog::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ExportDialog.stringdata0))
        return static_cast<void*>(this);
    return QDialog::qt_metacast(_clname);
}

int ExportDialog::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QDialog::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 23)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 23;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 23)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 23;
    }
    return _id;
}

// SIGNAL 0
void ExportDialog::exportStarted()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ExportDialog::exportProgress(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void ExportDialog::exportCompleted(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void ExportDialog::exportCancelled()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ExportDialog::exportError(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void ExportDialog::previewReady(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
