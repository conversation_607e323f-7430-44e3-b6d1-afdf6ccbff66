/****************************************************************************
** Meta object code from reading C++ file 'aboutdialog.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.4.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/ui/dialogs/aboutdialog.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'aboutdialog.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.4.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
namespace {
struct qt_meta_stringdata_AboutDialog_t {
    uint offsetsAndSizes[40];
    char stringdata0[12];
    char stringdata1[17];
    char stringdata2[1];
    char stringdata3[4];
    char stringdata4[23];
    char stringdata5[17];
    char stringdata6[21];
    char stringdata7[15];
    char stringdata8[12];
    char stringdata9[12];
    char stringdata10[12];
    char stringdata11[18];
    char stringdata12[12];
    char stringdata13[16];
    char stringdata14[13];
    char stringdata15[6];
    char stringdata16[17];
    char stringdata17[14];
    char stringdata18[14];
    char stringdata19[5];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_AboutDialog_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_AboutDialog_t qt_meta_stringdata_AboutDialog = {
    {
        QT_MOC_LITERAL(0, 11),  // "AboutDialog"
        QT_MOC_LITERAL(12, 16),  // "websiteRequested"
        QT_MOC_LITERAL(29, 0),  // ""
        QT_MOC_LITERAL(30, 3),  // "url"
        QT_MOC_LITERAL(34, 22),  // "documentationRequested"
        QT_MOC_LITERAL(57, 16),  // "supportRequested"
        QT_MOC_LITERAL(74, 20),  // "updateCheckRequested"
        QT_MOC_LITERAL(95, 14),  // "showSystemInfo"
        QT_MOC_LITERAL(110, 11),  // "showLicense"
        QT_MOC_LITERAL(122, 11),  // "showCredits"
        QT_MOC_LITERAL(134, 11),  // "openWebsite"
        QT_MOC_LITERAL(146, 17),  // "openDocumentation"
        QT_MOC_LITERAL(164, 11),  // "openSupport"
        QT_MOC_LITERAL(176, 15),  // "checkForUpdates"
        QT_MOC_LITERAL(192, 12),  // "onTabChanged"
        QT_MOC_LITERAL(205, 5),  // "index"
        QT_MOC_LITERAL(211, 16),  // "onAnimationTimer"
        QT_MOC_LITERAL(228, 13),  // "onLogoClicked"
        QT_MOC_LITERAL(242, 13),  // "onLinkClicked"
        QT_MOC_LITERAL(256, 4)   // "link"
    },
    "AboutDialog",
    "websiteRequested",
    "",
    "url",
    "documentationRequested",
    "supportRequested",
    "updateCheckRequested",
    "showSystemInfo",
    "showLicense",
    "showCredits",
    "openWebsite",
    "openDocumentation",
    "openSupport",
    "checkForUpdates",
    "onTabChanged",
    "index",
    "onAnimationTimer",
    "onLogoClicked",
    "onLinkClicked",
    "link"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_AboutDialog[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
      15,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    1,  104,    2, 0x06,    1 /* Public */,
       4,    1,  107,    2, 0x06,    3 /* Public */,
       5,    1,  110,    2, 0x06,    5 /* Public */,
       6,    0,  113,    2, 0x06,    7 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       7,    0,  114,    2, 0x0a,    8 /* Public */,
       8,    0,  115,    2, 0x0a,    9 /* Public */,
       9,    0,  116,    2, 0x0a,   10 /* Public */,
      10,    0,  117,    2, 0x0a,   11 /* Public */,
      11,    0,  118,    2, 0x0a,   12 /* Public */,
      12,    0,  119,    2, 0x0a,   13 /* Public */,
      13,    0,  120,    2, 0x0a,   14 /* Public */,
      14,    1,  121,    2, 0x08,   15 /* Private */,
      16,    0,  124,    2, 0x08,   17 /* Private */,
      17,    0,  125,    2, 0x08,   18 /* Private */,
      18,    1,  126,    2, 0x08,   19 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   15,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   19,

       0        // eod
};

Q_CONSTINIT const QMetaObject AboutDialog::staticMetaObject = { {
    QMetaObject::SuperData::link<QDialog::staticMetaObject>(),
    qt_meta_stringdata_AboutDialog.offsetsAndSizes,
    qt_meta_data_AboutDialog,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_AboutDialog_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<AboutDialog, std::true_type>,
        // method 'websiteRequested'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'documentationRequested'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'supportRequested'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'updateCheckRequested'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'showSystemInfo'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'showLicense'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'showCredits'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'openWebsite'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'openDocumentation'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'openSupport'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'checkForUpdates'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onTabChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'onAnimationTimer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onLogoClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onLinkClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>
    >,
    nullptr
} };

void AboutDialog::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<AboutDialog *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->websiteRequested((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 1: _t->documentationRequested((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 2: _t->supportRequested((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->updateCheckRequested(); break;
        case 4: _t->showSystemInfo(); break;
        case 5: _t->showLicense(); break;
        case 6: _t->showCredits(); break;
        case 7: _t->openWebsite(); break;
        case 8: _t->openDocumentation(); break;
        case 9: _t->openSupport(); break;
        case 10: _t->checkForUpdates(); break;
        case 11: _t->onTabChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 12: _t->onAnimationTimer(); break;
        case 13: _t->onLogoClicked(); break;
        case 14: _t->onLinkClicked((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (AboutDialog::*)(const QString & );
            if (_t _q_method = &AboutDialog::websiteRequested; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (AboutDialog::*)(const QString & );
            if (_t _q_method = &AboutDialog::documentationRequested; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (AboutDialog::*)(const QString & );
            if (_t _q_method = &AboutDialog::supportRequested; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (AboutDialog::*)();
            if (_t _q_method = &AboutDialog::updateCheckRequested; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
    }
}

const QMetaObject *AboutDialog::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AboutDialog::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_AboutDialog.stringdata0))
        return static_cast<void*>(this);
    return QDialog::qt_metacast(_clname);
}

int AboutDialog::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QDialog::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 15)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 15;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 15)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 15;
    }
    return _id;
}

// SIGNAL 0
void AboutDialog::websiteRequested(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void AboutDialog::documentationRequested(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void AboutDialog::supportRequested(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void AboutDialog::updateCheckRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
