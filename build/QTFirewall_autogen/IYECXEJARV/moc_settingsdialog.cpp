/****************************************************************************
** Meta object code from reading C++ file 'settingsdialog.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.4.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../src/ui/dialogs/settingsdialog.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'settingsdialog.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.4.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
namespace {
struct qt_meta_stringdata_SettingsDialog_t {
    uint offsetsAndSizes[48];
    char stringdata0[15];
    char stringdata1[16];
    char stringdata2[1];
    char stringdata3[16];
    char stringdata4[14];
    char stringdata5[20];
    char stringdata6[9];
    char stringdata7[7];
    char stringdata8[7];
    char stringdata9[14];
    char stringdata10[16];
    char stringdata11[15];
    char stringdata12[15];
    char stringdata13[15];
    char stringdata14[6];
    char stringdata15[13];
    char stringdata16[6];
    char stringdata17[17];
    char stringdata18[18];
    char stringdata19[20];
    char stringdata20[5];
    char stringdata21[22];
    char stringdata22[16];
    char stringdata23[16];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_SettingsDialog_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_SettingsDialog_t qt_meta_stringdata_SettingsDialog = {
    {
        QT_MOC_LITERAL(0, 14),  // "SettingsDialog"
        QT_MOC_LITERAL(15, 15),  // "settingsChanged"
        QT_MOC_LITERAL(31, 0),  // ""
        QT_MOC_LITERAL(32, 15),  // "settingsApplied"
        QT_MOC_LITERAL(48, 13),  // "settingsReset"
        QT_MOC_LITERAL(62, 19),  // "advancedModeChanged"
        QT_MOC_LITERAL(82, 8),  // "advanced"
        QT_MOC_LITERAL(91, 6),  // "accept"
        QT_MOC_LITERAL(98, 6),  // "reject"
        QT_MOC_LITERAL(105, 13),  // "applySettings"
        QT_MOC_LITERAL(119, 15),  // "restoreDefaults"
        QT_MOC_LITERAL(135, 14),  // "importSettings"
        QT_MOC_LITERAL(150, 14),  // "exportSettings"
        QT_MOC_LITERAL(165, 14),  // "searchSettings"
        QT_MOC_LITERAL(180, 5),  // "query"
        QT_MOC_LITERAL(186, 12),  // "onTabChanged"
        QT_MOC_LITERAL(199, 5),  // "index"
        QT_MOC_LITERAL(205, 16),  // "onSettingChanged"
        QT_MOC_LITERAL(222, 17),  // "onValidationTimer"
        QT_MOC_LITERAL(240, 19),  // "onSearchTextChanged"
        QT_MOC_LITERAL(260, 4),  // "text"
        QT_MOC_LITERAL(265, 21),  // "onAdvancedModeToggled"
        QT_MOC_LITERAL(287, 15),  // "onCategoryReset"
        QT_MOC_LITERAL(303, 15)   // "onHelpRequested"
    },
    "SettingsDialog",
    "settingsChanged",
    "",
    "settingsApplied",
    "settingsReset",
    "advancedModeChanged",
    "advanced",
    "accept",
    "reject",
    "applySettings",
    "restoreDefaults",
    "importSettings",
    "exportSettings",
    "searchSettings",
    "query",
    "onTabChanged",
    "index",
    "onSettingChanged",
    "onValidationTimer",
    "onSearchTextChanged",
    "text",
    "onAdvancedModeToggled",
    "onCategoryReset",
    "onHelpRequested"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_SettingsDialog[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
      18,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,  122,    2, 0x06,    1 /* Public */,
       3,    0,  123,    2, 0x06,    2 /* Public */,
       4,    0,  124,    2, 0x06,    3 /* Public */,
       5,    1,  125,    2, 0x06,    4 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       7,    0,  128,    2, 0x0a,    6 /* Public */,
       8,    0,  129,    2, 0x0a,    7 /* Public */,
       9,    0,  130,    2, 0x0a,    8 /* Public */,
      10,    0,  131,    2, 0x0a,    9 /* Public */,
      11,    0,  132,    2, 0x0a,   10 /* Public */,
      12,    0,  133,    2, 0x0a,   11 /* Public */,
      13,    1,  134,    2, 0x0a,   12 /* Public */,
      15,    1,  137,    2, 0x08,   14 /* Private */,
      17,    0,  140,    2, 0x08,   16 /* Private */,
      18,    0,  141,    2, 0x08,   17 /* Private */,
      19,    1,  142,    2, 0x08,   18 /* Private */,
      21,    1,  145,    2, 0x08,   20 /* Private */,
      22,    0,  148,    2, 0x08,   22 /* Private */,
      23,    0,  149,    2, 0x08,   23 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,    6,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   14,
    QMetaType::Void, QMetaType::Int,   16,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   20,
    QMetaType::Void, QMetaType::Bool,    6,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject SettingsDialog::staticMetaObject = { {
    QMetaObject::SuperData::link<QDialog::staticMetaObject>(),
    qt_meta_stringdata_SettingsDialog.offsetsAndSizes,
    qt_meta_data_SettingsDialog,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_SettingsDialog_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<SettingsDialog, std::true_type>,
        // method 'settingsChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'settingsApplied'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'settingsReset'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'advancedModeChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'accept'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'reject'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'applySettings'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'restoreDefaults'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'importSettings'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'exportSettings'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'searchSettings'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'onTabChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'onSettingChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onValidationTimer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onSearchTextChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'onAdvancedModeToggled'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'onCategoryReset'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onHelpRequested'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void SettingsDialog::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<SettingsDialog *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->settingsChanged(); break;
        case 1: _t->settingsApplied(); break;
        case 2: _t->settingsReset(); break;
        case 3: _t->advancedModeChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 4: _t->accept(); break;
        case 5: _t->reject(); break;
        case 6: _t->applySettings(); break;
        case 7: _t->restoreDefaults(); break;
        case 8: _t->importSettings(); break;
        case 9: _t->exportSettings(); break;
        case 10: _t->searchSettings((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 11: _t->onTabChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 12: _t->onSettingChanged(); break;
        case 13: _t->onValidationTimer(); break;
        case 14: _t->onSearchTextChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 15: _t->onAdvancedModeToggled((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 16: _t->onCategoryReset(); break;
        case 17: _t->onHelpRequested(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (SettingsDialog::*)();
            if (_t _q_method = &SettingsDialog::settingsChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (SettingsDialog::*)();
            if (_t _q_method = &SettingsDialog::settingsApplied; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (SettingsDialog::*)();
            if (_t _q_method = &SettingsDialog::settingsReset; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (SettingsDialog::*)(bool );
            if (_t _q_method = &SettingsDialog::advancedModeChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
    }
}

const QMetaObject *SettingsDialog::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *SettingsDialog::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_SettingsDialog.stringdata0))
        return static_cast<void*>(this);
    return QDialog::qt_metacast(_clname);
}

int SettingsDialog::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QDialog::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 18)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 18;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 18)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 18;
    }
    return _id;
}

// SIGNAL 0
void SettingsDialog::settingsChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void SettingsDialog::settingsApplied()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void SettingsDialog::settingsReset()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void SettingsDialog::advancedModeChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
