{"BUILD_DIR": "/home/<USER>/Desktop/dev/QT_Firewall/build/QTFirewall_autogen", "CMAKE_BINARY_DIR": "/home/<USER>/Desktop/dev/QT_Firewall/build", "CMAKE_CURRENT_BINARY_DIR": "/home/<USER>/Desktop/dev/QT_Firewall/build", "CMAKE_CURRENT_SOURCE_DIR": "/home/<USER>/Desktop/dev/QT_Firewall", "CMAKE_EXECUTABLE": "/usr/bin/cmake", "CMAKE_LIST_FILES": ["/home/<USER>/Desktop/dev/QT_Firewall/CMakeLists.txt", "/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles/3.25.1/CMakeSystem.cmake", "/usr/share/cmake-3.25/Modules/CMakeSystemSpecificInitialize.cmake", "/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles/3.25.1/CMakeCXXCompiler.cmake", "/usr/share/cmake-3.25/Modules/CMakeSystemSpecificInformation.cmake", "/usr/share/cmake-3.25/Modules/CMakeGenericSystem.cmake", "/usr/share/cmake-3.25/Modules/CMakeInitializeConfigs.cmake", "/usr/share/cmake-3.25/Modules/Platform/Linux.cmake", "/usr/share/cmake-3.25/Modules/Platform/UnixPaths.cmake", "/usr/share/cmake-3.25/Modules/CMakeCXXInformation.cmake", "/usr/share/cmake-3.25/Modules/CMakeLanguageInformation.cmake", "/usr/share/cmake-3.25/Modules/Compiler/GNU-CXX.cmake", "/usr/share/cmake-3.25/Modules/Compiler/GNU.cmake", "/usr/share/cmake-3.25/Modules/Compiler/CMakeCommonCompilerMacros.cmake", "/usr/share/cmake-3.25/Modules/Platform/Linux-GNU-CXX.cmake", "/usr/share/cmake-3.25/Modules/Platform/Linux-GNU.cmake", "/usr/share/cmake-3.25/Modules/CMakeCommonLanguageInclude.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6ConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6ConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6Config.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6ConfigExtras.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6Targets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6VersionlessTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtFeature.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtFeatureCommon.cmake", "/usr/share/cmake-3.25/Modules/CheckCXXCompilerFlag.cmake", "/usr/share/cmake-3.25/Modules/CheckCXXSourceCompiles.cmake", "/usr/share/cmake-3.25/Modules/Internal/CheckSourceCompiles.cmake", "/usr/share/cmake-3.25/Modules/Internal/CheckCompilerFlag.cmake", "/usr/share/cmake-3.25/Modules/Internal/CheckFlagCommonConfig.cmake", "/usr/share/cmake-3.25/Modules/Internal/CheckSourceCompiles.cmake", "/usr/share/cmake-3.25/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicAppleHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicFinalizerHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicPluginHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicTargetHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicWalkLibsHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicFindPackageHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicDependencyHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicTestHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicToolHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/usr/share/cmake-3.25/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6Dependencies.cmake", "/usr/share/cmake-3.25/Modules/FindThreads.cmake", "/usr/share/cmake-3.25/Modules/CheckLibraryExists.cmake", "/usr/share/cmake-3.25/Modules/CheckIncludeFileCXX.cmake", "/usr/share/cmake-3.25/Modules/CheckCXXSourceCompiles.cmake", "/usr/share/cmake-3.25/Modules/FindPackageHandleStandardArgs.cmake", "/usr/share/cmake-3.25/Modules/FindPackageMessage.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreConfig.cmake", "/usr/share/cmake-3.25/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/FindWrapAtomic.cmake", "/usr/share/cmake-3.25/Modules/CheckCXXSourceCompiles.cmake", "/usr/share/cmake-3.25/Modules/FindPackageHandleStandardArgs.cmake", "/usr/share/cmake-3.25/Modules/FindPackageMessage.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6CoreTools/Qt6CoreToolsConfig.cmake", "/usr/share/cmake-3.25/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6CoreTools/Qt6CoreToolsDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6CoreTools/Qt6CoreToolsTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6CoreTools/Qt6CoreToolsTargets-none.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6CoreTools/Qt6CoreToolsAdditionalTargetInfo.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6CoreTools/Qt6CoreToolsVersionlessTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreTargets-none.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreAdditionalTargetInfo.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreVersionlessTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreMacros.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreConfigExtras.cmake", "/usr/share/cmake-3.25/Modules/GNUInstallDirs.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Widgets/Qt6WidgetsConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Widgets/Qt6WidgetsConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "/usr/share/cmake-3.25/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfig.cmake", "/usr/share/cmake-3.25/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WidgetsTools/Qt6WidgetsToolsDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6GuiTools/Qt6GuiToolsConfig.cmake", "/usr/share/cmake-3.25/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6GuiTools/Qt6GuiToolsDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6GuiTools/Qt6GuiToolsTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6GuiTools/Qt6GuiToolsTargets-none.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6GuiTools/Qt6GuiToolsAdditionalTargetInfo.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6GuiTools/Qt6GuiToolsVersionlessTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WidgetsTools/Qt6WidgetsToolsTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WidgetsTools/Qt6WidgetsToolsTargets-none.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WidgetsTools/Qt6WidgetsToolsAdditionalTargetInfo.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WidgetsTools/Qt6WidgetsToolsVersionlessTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Gui/Qt6GuiConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Gui/Qt6GuiConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Gui/Qt6GuiConfig.cmake", "/usr/share/cmake-3.25/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Gui/Qt6GuiDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/FindWrapOpenGL.cmake", "/usr/share/cmake-3.25/Modules/FindOpenGL.cmake", "/usr/share/cmake-3.25/Modules/FindPackageHandleStandardArgs.cmake", "/usr/share/cmake-3.25/Modules/FindPackageMessage.cmake", "/usr/share/cmake-3.25/Modules/FindPackageHandleStandardArgs.cmake", "/usr/share/cmake-3.25/Modules/FindPackageMessage.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/3rdparty/kwin/FindXKB.cmake", "/usr/share/cmake-3.25/Modules/FindPkgConfig.cmake", "/usr/share/cmake-3.25/Modules/FindPackageHandleStandardArgs.cmake", "/usr/share/cmake-3.25/Modules/FindPackageMessage.cmake", "/usr/share/cmake-3.25/Modules/FindPackageHandleStandardArgs.cmake", "/usr/share/cmake-3.25/Modules/FindPackageMessage.cmake", "/usr/share/cmake-3.25/Modules/FeatureSummary.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/FindWrapVulkanHeaders.cmake", "/usr/share/cmake-3.25/Modules/FindVulkan.cmake", "/usr/share/cmake-3.25/Modules/FindPackageHandleStandardArgs.cmake", "/usr/share/cmake-3.25/Modules/FindPackageMessage.cmake", "/usr/share/cmake-3.25/Modules/FindPackageHandleStandardArgs.cmake", "/usr/share/cmake-3.25/Modules/FindPackageMessage.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6GuiTools/Qt6GuiToolsConfig.cmake", "/usr/share/cmake-3.25/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6GuiTools/Qt6GuiToolsDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6GuiTools/Qt6GuiToolsTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6GuiTools/Qt6GuiToolsAdditionalTargetInfo.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6GuiTools/Qt6GuiToolsVersionlessTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6DBus/Qt6DBusConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6DBus/Qt6DBusConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6DBus/Qt6DBusConfig.cmake", "/usr/share/cmake-3.25/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6DBus/Qt6DBusDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6DBusTools/Qt6DBusToolsConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6DBusTools/Qt6DBusToolsConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6DBusTools/Qt6DBusToolsConfig.cmake", "/usr/share/cmake-3.25/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6DBusTools/Qt6DBusToolsDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6DBusTools/Qt6DBusToolsTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6DBusTools/Qt6DBusToolsTargets-none.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6DBusTools/Qt6DBusToolsAdditionalTargetInfo.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6DBusTools/Qt6DBusToolsVersionlessTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6DBus/Qt6DBusTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6DBus/Qt6DBusTargets-none.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6DBus/Qt6DBusAdditionalTargetInfo.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6DBus/Qt6DBusVersionlessTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6DBus/Qt6DBusMacros.cmake", "/usr/share/cmake-3.25/Modules/MacroAddFileDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Gui/Qt6GuiTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Gui/Qt6GuiTargets-none.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Gui/Qt6GuiAdditionalTargetInfo.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Gui/Qt6GuiVersionlessTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Widgets/Qt6WidgetsTargets-none.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Widgets/Qt6WidgetsAdditionalTargetInfo.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Widgets/Qt6WidgetsVersionlessTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Widgets/Qt6WidgetsMacros.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Network/Qt6NetworkConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Network/Qt6NetworkConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Network/Qt6NetworkConfig.cmake", "/usr/share/cmake-3.25/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Network/Qt6NetworkDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Network/Qt6NetworkTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Network/Qt6NetworkTargets-none.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Network/Qt6NetworkAdditionalTargetInfo.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Network/Qt6NetworkVersionlessTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Charts/Qt6ChartsConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Charts/Qt6ChartsConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Charts/Qt6ChartsConfig.cmake", "/usr/share/cmake-3.25/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Charts/Qt6ChartsDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6OpenGL/Qt6OpenGLConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6OpenGL/Qt6OpenGLConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6OpenGL/Qt6OpenGLConfig.cmake", "/usr/share/cmake-3.25/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6OpenGL/Qt6OpenGLDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/FindWrapVulkanHeaders.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6OpenGL/Qt6OpenGLTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6OpenGL/Qt6OpenGLTargets-none.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6OpenGL/Qt6OpenGLAdditionalTargetInfo.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6OpenGL/Qt6OpenGLVersionlessTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6OpenGLWidgets/Qt6OpenGLWidgetsConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6OpenGLWidgets/Qt6OpenGLWidgetsConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6OpenGLWidgets/Qt6OpenGLWidgetsConfig.cmake", "/usr/share/cmake-3.25/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6OpenGLWidgets/Qt6OpenGLWidgetsDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6OpenGLWidgets/Qt6OpenGLWidgetsTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6OpenGLWidgets/Qt6OpenGLWidgetsTargets-none.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6OpenGLWidgets/Qt6OpenGLWidgetsAdditionalTargetInfo.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6OpenGLWidgets/Qt6OpenGLWidgetsVersionlessTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Charts/Qt6ChartsTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Charts/Qt6ChartsTargets-none.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Charts/Qt6ChartsAdditionalTargetInfo.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Charts/Qt6ChartsVersionlessTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Svg/Qt6SvgConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Svg/Qt6SvgConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Svg/Qt6SvgConfig.cmake", "/usr/share/cmake-3.25/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Svg/Qt6SvgDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Svg/Qt6SvgTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Svg/Qt6SvgTargets-none.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Svg/Qt6SvgAdditionalTargetInfo.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Svg/Qt6SvgVersionlessTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Concurrent/Qt6ConcurrentConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Concurrent/Qt6ConcurrentConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Concurrent/Qt6ConcurrentConfig.cmake", "/usr/share/cmake-3.25/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Concurrent/Qt6ConcurrentDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Concurrent/Qt6ConcurrentTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Concurrent/Qt6ConcurrentTargets-none.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Concurrent/Qt6ConcurrentAdditionalTargetInfo.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Concurrent/Qt6ConcurrentVersionlessTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6PrintSupport/Qt6PrintSupportConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6PrintSupport/Qt6PrintSupportConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6PrintSupport/Qt6PrintSupportConfig.cmake", "/usr/share/cmake-3.25/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6PrintSupport/Qt6PrintSupportDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6PrintSupport/Qt6PrintSupportTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6PrintSupport/Qt6PrintSupportTargets-none.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6PrintSupport/Qt6PrintSupportAdditionalTargetInfo.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6PrintSupport/Qt6PrintSupportVersionlessTargets.cmake", "/home/<USER>/Desktop/dev/QT_Firewall/resources/resources.qrc"], "CMAKE_SOURCE_DIR": "/home/<USER>/Desktop/dev/QT_Firewall", "DEP_FILE": "", "DEP_FILE_RULE_NAME": "", "HEADERS": [["/home/<USER>/Desktop/dev/QT_Firewall/src/core/application.h", "MU", "PRMOGMWJPH/moc_application.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/core/applicationcontroller.h", "MU", "PRMOGMWJPH/moc_applicationcontroller.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/models/alertmodel.h", "MU", "M4YTXQ7V2H/moc_alertmodel.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/models/configurationmodel.h", "MU", "M4YTXQ7V2H/moc_configurationmodel.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/models/networkinterfacemodel.h", "MU", "M4YTXQ7V2H/moc_networkinterfacemodel.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/models/packetmodel.h", "MU", "M4YTXQ7V2H/moc_packetmodel.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/models/rulemodel.h", "MU", "M4YTXQ7V2H/moc_rulemodel.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/network/arpanalyzer.h", "MU", "UFQQIFJZKK/moc_arpanalyzer.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/network/firewallengine.h", "MU", "UFQQIFJZKK/moc_firewallengine.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/network/ipanalyzer.h", "MU", "UFQQIFJZKK/moc_ipanalyzer.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/network/packetcapture.h", "MU", "UFQQIFJZKK/moc_packetcapture.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/network/protocolanalyzer.h", "MU", "UFQQIFJZKK/moc_protocolanalyzer.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/network/tcpanalyzer.h", "MU", "UFQQIFJZKK/moc_tcpanalyzer.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/network/udpanalyzer.h", "MU", "UFQQIFJZKK/moc_udpanalyzer.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/alertview.h", "MU", "YPKJ5OE7LN/moc_alertview.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/dashboardview.h", "MU", "YPKJ5OE7LN/moc_dashboardview.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/aboutdialog.h", "MU", "IYECXEJARV/moc_aboutdialog.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/exportdialog.h", "MU", "IYECXEJARV/moc_exportdialog.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/ruledialog.h", "MU", "IYECXEJARV/moc_ruledialog.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/settingsdialog.h", "MU", "IYECXEJARV/moc_settingsdialog.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/mainwindow.h", "MU", "YPKJ5OE7LN/moc_mainwindow.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/reportview.h", "MU", "YPKJ5OE7LN/moc_reportview.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/rulemanagerview.h", "MU", "YPKJ5OE7LN/moc_rulemanagerview.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/settingsview.h", "MU", "YPKJ5OE7LN/moc_settingsview.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/welcomewizard.h", "MU", "YPKJ5OE7LN/moc_welcomewizard.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/alertpanelwidget.h", "MU", "JCA2YNWUYB/moc_alertpanelwidget.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/animatedbutton.h", "MU", "JCA2YNWUYB/moc_animatedbutton.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/networkvisualizationwidget.h", "MU", "JCA2YNWUYB/moc_networkvisualizationwidget.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/packetflowwidget.h", "MU", "JCA2YNWUYB/moc_packetflowwidget.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/protocolfilterwidget.h", "MU", "JCA2YNWUYB/moc_protocolfilterwidget.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/rulebuilderwidget.h", "MU", "JCA2YNWUYB/moc_rulebuilderwidget.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/statisticswidget.h", "MU", "JCA2YNWUYB/moc_statisticswidget.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/utils/animationhelper.h", "MU", "VSCBVMNR7M/moc_animationhelper.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/utils/configmanager.h", "MU", "VSCBVMNR7M/moc_configmanager.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/utils/exportmanager.h", "MU", "VSCBVMNR7M/moc_exportmanager.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/utils/logger.h", "MU", "VSCBVMNR7M/moc_logger.cpp", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/utils/thememanager.h", "MU", "VSCBVMNR7M/moc_thememanager.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "/home/<USER>/Desktop/dev/QT_Firewall/build/QTFirewall_autogen/include", "MOC_COMPILATION_FILE": "/home/<USER>/Desktop/dev/QT_Firewall/build/QTFirewall_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["QT_CHARTS_LIB", "QT_CONCURRENT_LIB", "QT_CORE_LIB", "QT_GUI_LIB", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_OPENGLWIDGETS_LIB", "QT_OPENGL_LIB", "QT_PRINTSUPPORT_LIB", "QT_SVG_LIB", "QT_WIDGETS_LIB"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["/home/<USER>/Desktop/dev/QT_Firewall/src", "/home/<USER>/Desktop/dev/QT_Firewall/src/core", "/home/<USER>/Desktop/dev/QT_Firewall/src/models", "/home/<USER>/Desktop/dev/QT_Firewall/src/network", "/home/<USER>/Desktop/dev/QT_Firewall/src/ui", "/home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets", "/home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs", "/home/<USER>/Desktop/dev/QT_Firewall/src/ui/views", "/home/<USER>/Desktop/dev/QT_Firewall/src/utils", "/usr/include/x86_64-linux-gnu/qt6/QtCore", "/usr/include/x86_64-linux-gnu/qt6", "/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "/usr/include/x86_64-linux-gnu/qt6/QtWidgets", "/usr/include/x86_64-linux-gnu/qt6/QtGui", "/usr/include/x86_64-linux-gnu/qt6/QtNetwork", "/usr/include/x86_64-linux-gnu/qt6/QtCharts", "/usr/include/x86_64-linux-gnu/qt6/QtOpenGL", "/usr/include/x86_64-linux-gnu/qt6/QtOpenGLWidgets", "/usr/include/x86_64-linux-gnu/qt6/QtSvg", "/usr/include/x86_64-linux-gnu/qt6/QtConcurrent", "/usr/include/x86_64-linux-gnu/qt6/QtPrintSupport", "/usr/include", "/usr/include/c++/12", "/usr/include/x86_64-linux-gnu/c++/12", "/usr/include/c++/12/backward", "/usr/lib/gcc/x86_64-linux-gnu/12/include", "/usr/local/include", "/usr/include/x86_64-linux-gnu"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_GADGET_EXPORT", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["/usr/bin/c++", "-dM", "-E", "-c", "/usr/share/cmake-3.25/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "/home/<USER>/Desktop/dev/QT_Firewall/build/QTFirewall_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": false, "PARALLEL": 2, "PARSE_CACHE_FILE": "/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles/QTFirewall_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "/usr/lib/qt6/libexec/moc", "QT_UIC_EXECUTABLE": "/usr/lib/qt6/libexec/uic", "QT_VERSION_MAJOR": 6, "QT_VERSION_MINOR": 4, "SETTINGS_FILE": "/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles/QTFirewall_autogen.dir/AutogenUsed.txt", "SOURCES": [["/home/<USER>/Desktop/dev/QT_Firewall/src/core/application.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/core/applicationcontroller.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/main.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/models/alertmodel.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/models/configurationmodel.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/models/networkinterfacemodel.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/models/packetmodel.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/models/rulemodel.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/network/arpanalyzer.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/network/firewallengine.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/network/ipanalyzer.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/network/packetcapture.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/network/protocolanalyzer.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/network/tcpanalyzer.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/network/udpanalyzer.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/alertview.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/dashboardview.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/aboutdialog.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/exportdialog.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/ruledialog.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/settingsdialog.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/mainwindow.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/reportview.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/rulemanagerview.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/settingsview.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/welcomewizard.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/alertpanelwidget.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/animatedbutton.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/networkvisualizationwidget.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/packetflowwidget.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/protocolfilterwidget.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/rulebuilderwidget.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/statisticswidget.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/utils/animationhelper.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/utils/configmanager.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/utils/exportmanager.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/utils/logger.cpp", "MU", null], ["/home/<USER>/Desktop/dev/QT_Firewall/src/utils/thememanager.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "VERBOSITY": 0}