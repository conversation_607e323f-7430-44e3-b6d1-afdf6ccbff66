{"BUILD_DIR": "/home/<USER>/Desktop/dev/QT_Firewall/build/QTFirewall_autogen", "CMAKE_BINARY_DIR": "/home/<USER>/Desktop/dev/QT_Firewall/build", "CMAKE_CURRENT_BINARY_DIR": "/home/<USER>/Desktop/dev/QT_Firewall/build", "CMAKE_CURRENT_SOURCE_DIR": "/home/<USER>/Desktop/dev/QT_Firewall", "CMAKE_SOURCE_DIR": "/home/<USER>/Desktop/dev/QT_Firewall", "INCLUDE_DIR": "/home/<USER>/Desktop/dev/QT_Firewall/build/QTFirewall_autogen/include", "INPUTS": ["/home/<USER>/Desktop/dev/QT_Firewall/resources/themes/dark.qss", "/home/<USER>/Desktop/dev/QT_Firewall/resources/themes/light.qss"], "LOCK_FILE": "/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles/QTFirewall_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Lock.lock", "MULTI_CONFIG": false, "OPTIONS": ["-name", "resources"], "OUTPUT_CHECKSUM": "3YJK5W5UP7", "OUTPUT_NAME": "qrc_resources.cpp", "RCC_EXECUTABLE": "/usr/lib/qt6/libexec/rcc", "RCC_LIST_OPTIONS": ["--list"], "SETTINGS_FILE": "/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles/QTFirewall_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Used.txt", "SOURCE": "/home/<USER>/Desktop/dev/QT_Firewall/resources/resources.qrc", "VERBOSITY": 0}