# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/dev/QT_Firewall

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/dev/QT_Firewall/build

# Utility rule file for QTFirewall_autogen.

# Include any custom commands dependencies for this target.
include CMakeFiles/QTFirewall_autogen.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/QTFirewall_autogen.dir/progress.make

CMakeFiles/QTFirewall_autogen:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic MOC and UIC for target QTFirewall"
	/usr/bin/cmake -E cmake_autogen /home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles/QTFirewall_autogen.dir/AutogenInfo.json Debug

QTFirewall_autogen: CMakeFiles/QTFirewall_autogen
QTFirewall_autogen: CMakeFiles/QTFirewall_autogen.dir/build.make
.PHONY : QTFirewall_autogen

# Rule to build all files generated by this target.
CMakeFiles/QTFirewall_autogen.dir/build: QTFirewall_autogen
.PHONY : CMakeFiles/QTFirewall_autogen.dir/build

CMakeFiles/QTFirewall_autogen.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/QTFirewall_autogen.dir/cmake_clean.cmake
.PHONY : CMakeFiles/QTFirewall_autogen.dir/clean

CMakeFiles/QTFirewall_autogen.dir/depend:
	cd /home/<USER>/Desktop/dev/QT_Firewall/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/dev/QT_Firewall /home/<USER>/Desktop/dev/QT_Firewall /home/<USER>/Desktop/dev/QT_Firewall/build /home/<USER>/Desktop/dev/QT_Firewall/build /home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles/QTFirewall_autogen.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/QTFirewall_autogen.dir/depend

