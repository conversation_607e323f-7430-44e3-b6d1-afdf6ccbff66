# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# compile CXX with /usr/bin/g++
CXX_DEFINES = -DQT_CHARTS_LIB -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_OPENGLWIDGETS_LIB -DQT_OPENGL_LIB -DQT_PRINTSUPPORT_LIB -DQT_SVG_LIB -DQT_WIDGETS_LIB

CXX_INCLUDES = -I/home/<USER>/Desktop/dev/QT_Firewall/build/QTFirewall_autogen/include -I/home/<USER>/Desktop/dev/QT_Firewall/src -I/home/<USER>/Desktop/dev/QT_Firewall/src/core -I/home/<USER>/Desktop/dev/QT_Firewall/src/models -I/home/<USER>/Desktop/dev/QT_Firewall/src/network -I/home/<USER>/Desktop/dev/QT_Firewall/src/ui -I/home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets -I/home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs -I/home/<USER>/Desktop/dev/QT_Firewall/src/ui/views -I/home/<USER>/Desktop/dev/QT_Firewall/src/utils -isystem /usr/include/x86_64-linux-gnu/qt6/QtCore -isystem /usr/include/x86_64-linux-gnu/qt6 -isystem /usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++ -isystem /usr/include/x86_64-linux-gnu/qt6/QtWidgets -isystem /usr/include/x86_64-linux-gnu/qt6/QtGui -isystem /usr/include/x86_64-linux-gnu/qt6/QtNetwork -isystem /usr/include/x86_64-linux-gnu/qt6/QtCharts -isystem /usr/include/x86_64-linux-gnu/qt6/QtOpenGL -isystem /usr/include/x86_64-linux-gnu/qt6/QtOpenGLWidgets -isystem /usr/include/x86_64-linux-gnu/qt6/QtSvg -isystem /usr/include/x86_64-linux-gnu/qt6/QtConcurrent -isystem /usr/include/x86_64-linux-gnu/qt6/QtPrintSupport

CXX_FLAGS = -g -Wall -Wextra -Wpedantic -fPIC -std=gnu++17

