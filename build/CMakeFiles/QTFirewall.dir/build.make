# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/dev/QT_Firewall

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/dev/QT_Firewall/build

# Include any dependencies generated for this target.
include CMakeFiles/QTFirewall.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/QTFirewall.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/QTFirewall.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/QTFirewall.dir/flags.make

QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp: /home/<USER>/Desktop/dev/QT_Firewall/resources/resources.qrc
QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp: CMakeFiles/QTFirewall_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json
QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp: /home/<USER>/Desktop/dev/QT_Firewall/resources/themes/dark.qss
QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp: /home/<USER>/Desktop/dev/QT_Firewall/resources/themes/light.qss
QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp: /usr/lib/qt6/libexec/rcc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic RCC for resources/resources.qrc"
	/usr/bin/cmake -E cmake_autorcc /home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles/QTFirewall_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json 

CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.o: QTFirewall_autogen/mocs_compilation.cpp
CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.o -MF CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/build/QTFirewall_autogen/mocs_compilation.cpp

CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/build/QTFirewall_autogen/mocs_compilation.cpp > CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.i

CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/build/QTFirewall_autogen/mocs_compilation.cpp -o CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.s

CMakeFiles/QTFirewall.dir/src/main.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/main.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/main.cpp
CMakeFiles/QTFirewall.dir/src/main.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/QTFirewall.dir/src/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/main.cpp.o -MF CMakeFiles/QTFirewall.dir/src/main.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/main.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/main.cpp

CMakeFiles/QTFirewall.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/main.cpp > CMakeFiles/QTFirewall.dir/src/main.cpp.i

CMakeFiles/QTFirewall.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/main.cpp -o CMakeFiles/QTFirewall.dir/src/main.cpp.s

CMakeFiles/QTFirewall.dir/src/core/application.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/core/application.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/core/application.cpp
CMakeFiles/QTFirewall.dir/src/core/application.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/QTFirewall.dir/src/core/application.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/core/application.cpp.o -MF CMakeFiles/QTFirewall.dir/src/core/application.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/core/application.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/core/application.cpp

CMakeFiles/QTFirewall.dir/src/core/application.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/core/application.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/core/application.cpp > CMakeFiles/QTFirewall.dir/src/core/application.cpp.i

CMakeFiles/QTFirewall.dir/src/core/application.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/core/application.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/core/application.cpp -o CMakeFiles/QTFirewall.dir/src/core/application.cpp.s

CMakeFiles/QTFirewall.dir/src/core/applicationcontroller.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/core/applicationcontroller.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/core/applicationcontroller.cpp
CMakeFiles/QTFirewall.dir/src/core/applicationcontroller.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/QTFirewall.dir/src/core/applicationcontroller.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/core/applicationcontroller.cpp.o -MF CMakeFiles/QTFirewall.dir/src/core/applicationcontroller.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/core/applicationcontroller.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/core/applicationcontroller.cpp

CMakeFiles/QTFirewall.dir/src/core/applicationcontroller.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/core/applicationcontroller.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/core/applicationcontroller.cpp > CMakeFiles/QTFirewall.dir/src/core/applicationcontroller.cpp.i

CMakeFiles/QTFirewall.dir/src/core/applicationcontroller.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/core/applicationcontroller.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/core/applicationcontroller.cpp -o CMakeFiles/QTFirewall.dir/src/core/applicationcontroller.cpp.s

CMakeFiles/QTFirewall.dir/src/models/packetmodel.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/models/packetmodel.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/models/packetmodel.cpp
CMakeFiles/QTFirewall.dir/src/models/packetmodel.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/QTFirewall.dir/src/models/packetmodel.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/models/packetmodel.cpp.o -MF CMakeFiles/QTFirewall.dir/src/models/packetmodel.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/models/packetmodel.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/models/packetmodel.cpp

CMakeFiles/QTFirewall.dir/src/models/packetmodel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/models/packetmodel.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/models/packetmodel.cpp > CMakeFiles/QTFirewall.dir/src/models/packetmodel.cpp.i

CMakeFiles/QTFirewall.dir/src/models/packetmodel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/models/packetmodel.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/models/packetmodel.cpp -o CMakeFiles/QTFirewall.dir/src/models/packetmodel.cpp.s

CMakeFiles/QTFirewall.dir/src/models/rulemodel.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/models/rulemodel.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/models/rulemodel.cpp
CMakeFiles/QTFirewall.dir/src/models/rulemodel.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/QTFirewall.dir/src/models/rulemodel.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/models/rulemodel.cpp.o -MF CMakeFiles/QTFirewall.dir/src/models/rulemodel.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/models/rulemodel.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/models/rulemodel.cpp

CMakeFiles/QTFirewall.dir/src/models/rulemodel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/models/rulemodel.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/models/rulemodel.cpp > CMakeFiles/QTFirewall.dir/src/models/rulemodel.cpp.i

CMakeFiles/QTFirewall.dir/src/models/rulemodel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/models/rulemodel.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/models/rulemodel.cpp -o CMakeFiles/QTFirewall.dir/src/models/rulemodel.cpp.s

CMakeFiles/QTFirewall.dir/src/models/alertmodel.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/models/alertmodel.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/models/alertmodel.cpp
CMakeFiles/QTFirewall.dir/src/models/alertmodel.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/QTFirewall.dir/src/models/alertmodel.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/models/alertmodel.cpp.o -MF CMakeFiles/QTFirewall.dir/src/models/alertmodel.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/models/alertmodel.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/models/alertmodel.cpp

CMakeFiles/QTFirewall.dir/src/models/alertmodel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/models/alertmodel.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/models/alertmodel.cpp > CMakeFiles/QTFirewall.dir/src/models/alertmodel.cpp.i

CMakeFiles/QTFirewall.dir/src/models/alertmodel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/models/alertmodel.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/models/alertmodel.cpp -o CMakeFiles/QTFirewall.dir/src/models/alertmodel.cpp.s

CMakeFiles/QTFirewall.dir/src/models/networkinterfacemodel.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/models/networkinterfacemodel.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/models/networkinterfacemodel.cpp
CMakeFiles/QTFirewall.dir/src/models/networkinterfacemodel.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/QTFirewall.dir/src/models/networkinterfacemodel.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/models/networkinterfacemodel.cpp.o -MF CMakeFiles/QTFirewall.dir/src/models/networkinterfacemodel.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/models/networkinterfacemodel.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/models/networkinterfacemodel.cpp

CMakeFiles/QTFirewall.dir/src/models/networkinterfacemodel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/models/networkinterfacemodel.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/models/networkinterfacemodel.cpp > CMakeFiles/QTFirewall.dir/src/models/networkinterfacemodel.cpp.i

CMakeFiles/QTFirewall.dir/src/models/networkinterfacemodel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/models/networkinterfacemodel.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/models/networkinterfacemodel.cpp -o CMakeFiles/QTFirewall.dir/src/models/networkinterfacemodel.cpp.s

CMakeFiles/QTFirewall.dir/src/models/configurationmodel.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/models/configurationmodel.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/models/configurationmodel.cpp
CMakeFiles/QTFirewall.dir/src/models/configurationmodel.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/QTFirewall.dir/src/models/configurationmodel.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/models/configurationmodel.cpp.o -MF CMakeFiles/QTFirewall.dir/src/models/configurationmodel.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/models/configurationmodel.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/models/configurationmodel.cpp

CMakeFiles/QTFirewall.dir/src/models/configurationmodel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/models/configurationmodel.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/models/configurationmodel.cpp > CMakeFiles/QTFirewall.dir/src/models/configurationmodel.cpp.i

CMakeFiles/QTFirewall.dir/src/models/configurationmodel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/models/configurationmodel.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/models/configurationmodel.cpp -o CMakeFiles/QTFirewall.dir/src/models/configurationmodel.cpp.s

CMakeFiles/QTFirewall.dir/src/network/packetcapture.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/network/packetcapture.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/network/packetcapture.cpp
CMakeFiles/QTFirewall.dir/src/network/packetcapture.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/QTFirewall.dir/src/network/packetcapture.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/network/packetcapture.cpp.o -MF CMakeFiles/QTFirewall.dir/src/network/packetcapture.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/network/packetcapture.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/network/packetcapture.cpp

CMakeFiles/QTFirewall.dir/src/network/packetcapture.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/network/packetcapture.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/network/packetcapture.cpp > CMakeFiles/QTFirewall.dir/src/network/packetcapture.cpp.i

CMakeFiles/QTFirewall.dir/src/network/packetcapture.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/network/packetcapture.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/network/packetcapture.cpp -o CMakeFiles/QTFirewall.dir/src/network/packetcapture.cpp.s

CMakeFiles/QTFirewall.dir/src/network/protocolanalyzer.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/network/protocolanalyzer.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/network/protocolanalyzer.cpp
CMakeFiles/QTFirewall.dir/src/network/protocolanalyzer.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object CMakeFiles/QTFirewall.dir/src/network/protocolanalyzer.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/network/protocolanalyzer.cpp.o -MF CMakeFiles/QTFirewall.dir/src/network/protocolanalyzer.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/network/protocolanalyzer.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/network/protocolanalyzer.cpp

CMakeFiles/QTFirewall.dir/src/network/protocolanalyzer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/network/protocolanalyzer.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/network/protocolanalyzer.cpp > CMakeFiles/QTFirewall.dir/src/network/protocolanalyzer.cpp.i

CMakeFiles/QTFirewall.dir/src/network/protocolanalyzer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/network/protocolanalyzer.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/network/protocolanalyzer.cpp -o CMakeFiles/QTFirewall.dir/src/network/protocolanalyzer.cpp.s

CMakeFiles/QTFirewall.dir/src/network/ipanalyzer.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/network/ipanalyzer.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/network/ipanalyzer.cpp
CMakeFiles/QTFirewall.dir/src/network/ipanalyzer.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object CMakeFiles/QTFirewall.dir/src/network/ipanalyzer.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/network/ipanalyzer.cpp.o -MF CMakeFiles/QTFirewall.dir/src/network/ipanalyzer.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/network/ipanalyzer.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/network/ipanalyzer.cpp

CMakeFiles/QTFirewall.dir/src/network/ipanalyzer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/network/ipanalyzer.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/network/ipanalyzer.cpp > CMakeFiles/QTFirewall.dir/src/network/ipanalyzer.cpp.i

CMakeFiles/QTFirewall.dir/src/network/ipanalyzer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/network/ipanalyzer.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/network/ipanalyzer.cpp -o CMakeFiles/QTFirewall.dir/src/network/ipanalyzer.cpp.s

CMakeFiles/QTFirewall.dir/src/network/arpanalyzer.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/network/arpanalyzer.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/network/arpanalyzer.cpp
CMakeFiles/QTFirewall.dir/src/network/arpanalyzer.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object CMakeFiles/QTFirewall.dir/src/network/arpanalyzer.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/network/arpanalyzer.cpp.o -MF CMakeFiles/QTFirewall.dir/src/network/arpanalyzer.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/network/arpanalyzer.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/network/arpanalyzer.cpp

CMakeFiles/QTFirewall.dir/src/network/arpanalyzer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/network/arpanalyzer.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/network/arpanalyzer.cpp > CMakeFiles/QTFirewall.dir/src/network/arpanalyzer.cpp.i

CMakeFiles/QTFirewall.dir/src/network/arpanalyzer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/network/arpanalyzer.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/network/arpanalyzer.cpp -o CMakeFiles/QTFirewall.dir/src/network/arpanalyzer.cpp.s

CMakeFiles/QTFirewall.dir/src/network/tcpanalyzer.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/network/tcpanalyzer.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/network/tcpanalyzer.cpp
CMakeFiles/QTFirewall.dir/src/network/tcpanalyzer.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object CMakeFiles/QTFirewall.dir/src/network/tcpanalyzer.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/network/tcpanalyzer.cpp.o -MF CMakeFiles/QTFirewall.dir/src/network/tcpanalyzer.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/network/tcpanalyzer.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/network/tcpanalyzer.cpp

CMakeFiles/QTFirewall.dir/src/network/tcpanalyzer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/network/tcpanalyzer.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/network/tcpanalyzer.cpp > CMakeFiles/QTFirewall.dir/src/network/tcpanalyzer.cpp.i

CMakeFiles/QTFirewall.dir/src/network/tcpanalyzer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/network/tcpanalyzer.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/network/tcpanalyzer.cpp -o CMakeFiles/QTFirewall.dir/src/network/tcpanalyzer.cpp.s

CMakeFiles/QTFirewall.dir/src/network/udpanalyzer.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/network/udpanalyzer.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/network/udpanalyzer.cpp
CMakeFiles/QTFirewall.dir/src/network/udpanalyzer.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object CMakeFiles/QTFirewall.dir/src/network/udpanalyzer.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/network/udpanalyzer.cpp.o -MF CMakeFiles/QTFirewall.dir/src/network/udpanalyzer.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/network/udpanalyzer.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/network/udpanalyzer.cpp

CMakeFiles/QTFirewall.dir/src/network/udpanalyzer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/network/udpanalyzer.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/network/udpanalyzer.cpp > CMakeFiles/QTFirewall.dir/src/network/udpanalyzer.cpp.i

CMakeFiles/QTFirewall.dir/src/network/udpanalyzer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/network/udpanalyzer.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/network/udpanalyzer.cpp -o CMakeFiles/QTFirewall.dir/src/network/udpanalyzer.cpp.s

CMakeFiles/QTFirewall.dir/src/network/firewallengine.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/network/firewallengine.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/network/firewallengine.cpp
CMakeFiles/QTFirewall.dir/src/network/firewallengine.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object CMakeFiles/QTFirewall.dir/src/network/firewallengine.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/network/firewallengine.cpp.o -MF CMakeFiles/QTFirewall.dir/src/network/firewallengine.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/network/firewallengine.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/network/firewallengine.cpp

CMakeFiles/QTFirewall.dir/src/network/firewallengine.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/network/firewallengine.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/network/firewallengine.cpp > CMakeFiles/QTFirewall.dir/src/network/firewallengine.cpp.i

CMakeFiles/QTFirewall.dir/src/network/firewallengine.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/network/firewallengine.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/network/firewallengine.cpp -o CMakeFiles/QTFirewall.dir/src/network/firewallengine.cpp.s

CMakeFiles/QTFirewall.dir/src/ui/mainwindow.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/ui/mainwindow.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/ui/mainwindow.cpp
CMakeFiles/QTFirewall.dir/src/ui/mainwindow.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object CMakeFiles/QTFirewall.dir/src/ui/mainwindow.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/ui/mainwindow.cpp.o -MF CMakeFiles/QTFirewall.dir/src/ui/mainwindow.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/ui/mainwindow.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/ui/mainwindow.cpp

CMakeFiles/QTFirewall.dir/src/ui/mainwindow.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/ui/mainwindow.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/ui/mainwindow.cpp > CMakeFiles/QTFirewall.dir/src/ui/mainwindow.cpp.i

CMakeFiles/QTFirewall.dir/src/ui/mainwindow.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/ui/mainwindow.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/ui/mainwindow.cpp -o CMakeFiles/QTFirewall.dir/src/ui/mainwindow.cpp.s

CMakeFiles/QTFirewall.dir/src/ui/welcomewizard.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/ui/welcomewizard.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/ui/welcomewizard.cpp
CMakeFiles/QTFirewall.dir/src/ui/welcomewizard.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building CXX object CMakeFiles/QTFirewall.dir/src/ui/welcomewizard.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/ui/welcomewizard.cpp.o -MF CMakeFiles/QTFirewall.dir/src/ui/welcomewizard.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/ui/welcomewizard.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/ui/welcomewizard.cpp

CMakeFiles/QTFirewall.dir/src/ui/welcomewizard.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/ui/welcomewizard.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/ui/welcomewizard.cpp > CMakeFiles/QTFirewall.dir/src/ui/welcomewizard.cpp.i

CMakeFiles/QTFirewall.dir/src/ui/welcomewizard.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/ui/welcomewizard.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/ui/welcomewizard.cpp -o CMakeFiles/QTFirewall.dir/src/ui/welcomewizard.cpp.s

CMakeFiles/QTFirewall.dir/src/ui/dashboardview.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/ui/dashboardview.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/ui/dashboardview.cpp
CMakeFiles/QTFirewall.dir/src/ui/dashboardview.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building CXX object CMakeFiles/QTFirewall.dir/src/ui/dashboardview.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/ui/dashboardview.cpp.o -MF CMakeFiles/QTFirewall.dir/src/ui/dashboardview.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/ui/dashboardview.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/ui/dashboardview.cpp

CMakeFiles/QTFirewall.dir/src/ui/dashboardview.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/ui/dashboardview.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/ui/dashboardview.cpp > CMakeFiles/QTFirewall.dir/src/ui/dashboardview.cpp.i

CMakeFiles/QTFirewall.dir/src/ui/dashboardview.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/ui/dashboardview.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/ui/dashboardview.cpp -o CMakeFiles/QTFirewall.dir/src/ui/dashboardview.cpp.s

CMakeFiles/QTFirewall.dir/src/ui/rulemanagerview.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/ui/rulemanagerview.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/ui/rulemanagerview.cpp
CMakeFiles/QTFirewall.dir/src/ui/rulemanagerview.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building CXX object CMakeFiles/QTFirewall.dir/src/ui/rulemanagerview.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/ui/rulemanagerview.cpp.o -MF CMakeFiles/QTFirewall.dir/src/ui/rulemanagerview.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/ui/rulemanagerview.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/ui/rulemanagerview.cpp

CMakeFiles/QTFirewall.dir/src/ui/rulemanagerview.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/ui/rulemanagerview.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/ui/rulemanagerview.cpp > CMakeFiles/QTFirewall.dir/src/ui/rulemanagerview.cpp.i

CMakeFiles/QTFirewall.dir/src/ui/rulemanagerview.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/ui/rulemanagerview.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/ui/rulemanagerview.cpp -o CMakeFiles/QTFirewall.dir/src/ui/rulemanagerview.cpp.s

CMakeFiles/QTFirewall.dir/src/ui/alertview.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/ui/alertview.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/ui/alertview.cpp
CMakeFiles/QTFirewall.dir/src/ui/alertview.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building CXX object CMakeFiles/QTFirewall.dir/src/ui/alertview.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/ui/alertview.cpp.o -MF CMakeFiles/QTFirewall.dir/src/ui/alertview.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/ui/alertview.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/ui/alertview.cpp

CMakeFiles/QTFirewall.dir/src/ui/alertview.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/ui/alertview.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/ui/alertview.cpp > CMakeFiles/QTFirewall.dir/src/ui/alertview.cpp.i

CMakeFiles/QTFirewall.dir/src/ui/alertview.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/ui/alertview.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/ui/alertview.cpp -o CMakeFiles/QTFirewall.dir/src/ui/alertview.cpp.s

CMakeFiles/QTFirewall.dir/src/ui/reportview.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/ui/reportview.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/ui/reportview.cpp
CMakeFiles/QTFirewall.dir/src/ui/reportview.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building CXX object CMakeFiles/QTFirewall.dir/src/ui/reportview.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/ui/reportview.cpp.o -MF CMakeFiles/QTFirewall.dir/src/ui/reportview.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/ui/reportview.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/ui/reportview.cpp

CMakeFiles/QTFirewall.dir/src/ui/reportview.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/ui/reportview.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/ui/reportview.cpp > CMakeFiles/QTFirewall.dir/src/ui/reportview.cpp.i

CMakeFiles/QTFirewall.dir/src/ui/reportview.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/ui/reportview.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/ui/reportview.cpp -o CMakeFiles/QTFirewall.dir/src/ui/reportview.cpp.s

CMakeFiles/QTFirewall.dir/src/ui/settingsview.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/ui/settingsview.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/ui/settingsview.cpp
CMakeFiles/QTFirewall.dir/src/ui/settingsview.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building CXX object CMakeFiles/QTFirewall.dir/src/ui/settingsview.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/ui/settingsview.cpp.o -MF CMakeFiles/QTFirewall.dir/src/ui/settingsview.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/ui/settingsview.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/ui/settingsview.cpp

CMakeFiles/QTFirewall.dir/src/ui/settingsview.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/ui/settingsview.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/ui/settingsview.cpp > CMakeFiles/QTFirewall.dir/src/ui/settingsview.cpp.i

CMakeFiles/QTFirewall.dir/src/ui/settingsview.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/ui/settingsview.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/ui/settingsview.cpp -o CMakeFiles/QTFirewall.dir/src/ui/settingsview.cpp.s

CMakeFiles/QTFirewall.dir/src/ui/widgets/networkvisualizationwidget.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/ui/widgets/networkvisualizationwidget.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/networkvisualizationwidget.cpp
CMakeFiles/QTFirewall.dir/src/ui/widgets/networkvisualizationwidget.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building CXX object CMakeFiles/QTFirewall.dir/src/ui/widgets/networkvisualizationwidget.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/ui/widgets/networkvisualizationwidget.cpp.o -MF CMakeFiles/QTFirewall.dir/src/ui/widgets/networkvisualizationwidget.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/ui/widgets/networkvisualizationwidget.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/networkvisualizationwidget.cpp

CMakeFiles/QTFirewall.dir/src/ui/widgets/networkvisualizationwidget.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/ui/widgets/networkvisualizationwidget.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/networkvisualizationwidget.cpp > CMakeFiles/QTFirewall.dir/src/ui/widgets/networkvisualizationwidget.cpp.i

CMakeFiles/QTFirewall.dir/src/ui/widgets/networkvisualizationwidget.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/ui/widgets/networkvisualizationwidget.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/networkvisualizationwidget.cpp -o CMakeFiles/QTFirewall.dir/src/ui/widgets/networkvisualizationwidget.cpp.s

CMakeFiles/QTFirewall.dir/src/ui/widgets/packetflowwidget.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/ui/widgets/packetflowwidget.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/packetflowwidget.cpp
CMakeFiles/QTFirewall.dir/src/ui/widgets/packetflowwidget.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building CXX object CMakeFiles/QTFirewall.dir/src/ui/widgets/packetflowwidget.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/ui/widgets/packetflowwidget.cpp.o -MF CMakeFiles/QTFirewall.dir/src/ui/widgets/packetflowwidget.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/ui/widgets/packetflowwidget.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/packetflowwidget.cpp

CMakeFiles/QTFirewall.dir/src/ui/widgets/packetflowwidget.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/ui/widgets/packetflowwidget.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/packetflowwidget.cpp > CMakeFiles/QTFirewall.dir/src/ui/widgets/packetflowwidget.cpp.i

CMakeFiles/QTFirewall.dir/src/ui/widgets/packetflowwidget.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/ui/widgets/packetflowwidget.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/packetflowwidget.cpp -o CMakeFiles/QTFirewall.dir/src/ui/widgets/packetflowwidget.cpp.s

CMakeFiles/QTFirewall.dir/src/ui/widgets/rulebuilderwidget.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/ui/widgets/rulebuilderwidget.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/rulebuilderwidget.cpp
CMakeFiles/QTFirewall.dir/src/ui/widgets/rulebuilderwidget.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building CXX object CMakeFiles/QTFirewall.dir/src/ui/widgets/rulebuilderwidget.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/ui/widgets/rulebuilderwidget.cpp.o -MF CMakeFiles/QTFirewall.dir/src/ui/widgets/rulebuilderwidget.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/ui/widgets/rulebuilderwidget.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/rulebuilderwidget.cpp

CMakeFiles/QTFirewall.dir/src/ui/widgets/rulebuilderwidget.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/ui/widgets/rulebuilderwidget.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/rulebuilderwidget.cpp > CMakeFiles/QTFirewall.dir/src/ui/widgets/rulebuilderwidget.cpp.i

CMakeFiles/QTFirewall.dir/src/ui/widgets/rulebuilderwidget.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/ui/widgets/rulebuilderwidget.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/rulebuilderwidget.cpp -o CMakeFiles/QTFirewall.dir/src/ui/widgets/rulebuilderwidget.cpp.s

CMakeFiles/QTFirewall.dir/src/ui/widgets/alertpanelwidget.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/ui/widgets/alertpanelwidget.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/alertpanelwidget.cpp
CMakeFiles/QTFirewall.dir/src/ui/widgets/alertpanelwidget.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building CXX object CMakeFiles/QTFirewall.dir/src/ui/widgets/alertpanelwidget.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/ui/widgets/alertpanelwidget.cpp.o -MF CMakeFiles/QTFirewall.dir/src/ui/widgets/alertpanelwidget.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/ui/widgets/alertpanelwidget.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/alertpanelwidget.cpp

CMakeFiles/QTFirewall.dir/src/ui/widgets/alertpanelwidget.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/ui/widgets/alertpanelwidget.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/alertpanelwidget.cpp > CMakeFiles/QTFirewall.dir/src/ui/widgets/alertpanelwidget.cpp.i

CMakeFiles/QTFirewall.dir/src/ui/widgets/alertpanelwidget.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/ui/widgets/alertpanelwidget.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/alertpanelwidget.cpp -o CMakeFiles/QTFirewall.dir/src/ui/widgets/alertpanelwidget.cpp.s

CMakeFiles/QTFirewall.dir/src/ui/widgets/statisticswidget.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/ui/widgets/statisticswidget.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/statisticswidget.cpp
CMakeFiles/QTFirewall.dir/src/ui/widgets/statisticswidget.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building CXX object CMakeFiles/QTFirewall.dir/src/ui/widgets/statisticswidget.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/ui/widgets/statisticswidget.cpp.o -MF CMakeFiles/QTFirewall.dir/src/ui/widgets/statisticswidget.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/ui/widgets/statisticswidget.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/statisticswidget.cpp

CMakeFiles/QTFirewall.dir/src/ui/widgets/statisticswidget.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/ui/widgets/statisticswidget.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/statisticswidget.cpp > CMakeFiles/QTFirewall.dir/src/ui/widgets/statisticswidget.cpp.i

CMakeFiles/QTFirewall.dir/src/ui/widgets/statisticswidget.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/ui/widgets/statisticswidget.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/statisticswidget.cpp -o CMakeFiles/QTFirewall.dir/src/ui/widgets/statisticswidget.cpp.s

CMakeFiles/QTFirewall.dir/src/ui/widgets/animatedbutton.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/ui/widgets/animatedbutton.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/animatedbutton.cpp
CMakeFiles/QTFirewall.dir/src/ui/widgets/animatedbutton.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building CXX object CMakeFiles/QTFirewall.dir/src/ui/widgets/animatedbutton.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/ui/widgets/animatedbutton.cpp.o -MF CMakeFiles/QTFirewall.dir/src/ui/widgets/animatedbutton.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/ui/widgets/animatedbutton.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/animatedbutton.cpp

CMakeFiles/QTFirewall.dir/src/ui/widgets/animatedbutton.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/ui/widgets/animatedbutton.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/animatedbutton.cpp > CMakeFiles/QTFirewall.dir/src/ui/widgets/animatedbutton.cpp.i

CMakeFiles/QTFirewall.dir/src/ui/widgets/animatedbutton.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/ui/widgets/animatedbutton.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/animatedbutton.cpp -o CMakeFiles/QTFirewall.dir/src/ui/widgets/animatedbutton.cpp.s

CMakeFiles/QTFirewall.dir/src/ui/widgets/protocolfilterwidget.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/ui/widgets/protocolfilterwidget.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/protocolfilterwidget.cpp
CMakeFiles/QTFirewall.dir/src/ui/widgets/protocolfilterwidget.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building CXX object CMakeFiles/QTFirewall.dir/src/ui/widgets/protocolfilterwidget.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/ui/widgets/protocolfilterwidget.cpp.o -MF CMakeFiles/QTFirewall.dir/src/ui/widgets/protocolfilterwidget.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/ui/widgets/protocolfilterwidget.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/protocolfilterwidget.cpp

CMakeFiles/QTFirewall.dir/src/ui/widgets/protocolfilterwidget.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/ui/widgets/protocolfilterwidget.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/protocolfilterwidget.cpp > CMakeFiles/QTFirewall.dir/src/ui/widgets/protocolfilterwidget.cpp.i

CMakeFiles/QTFirewall.dir/src/ui/widgets/protocolfilterwidget.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/ui/widgets/protocolfilterwidget.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/ui/widgets/protocolfilterwidget.cpp -o CMakeFiles/QTFirewall.dir/src/ui/widgets/protocolfilterwidget.cpp.s

CMakeFiles/QTFirewall.dir/src/ui/dialogs/ruledialog.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/ui/dialogs/ruledialog.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/ruledialog.cpp
CMakeFiles/QTFirewall.dir/src/ui/dialogs/ruledialog.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building CXX object CMakeFiles/QTFirewall.dir/src/ui/dialogs/ruledialog.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/ui/dialogs/ruledialog.cpp.o -MF CMakeFiles/QTFirewall.dir/src/ui/dialogs/ruledialog.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/ui/dialogs/ruledialog.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/ruledialog.cpp

CMakeFiles/QTFirewall.dir/src/ui/dialogs/ruledialog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/ui/dialogs/ruledialog.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/ruledialog.cpp > CMakeFiles/QTFirewall.dir/src/ui/dialogs/ruledialog.cpp.i

CMakeFiles/QTFirewall.dir/src/ui/dialogs/ruledialog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/ui/dialogs/ruledialog.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/ruledialog.cpp -o CMakeFiles/QTFirewall.dir/src/ui/dialogs/ruledialog.cpp.s

CMakeFiles/QTFirewall.dir/src/ui/dialogs/settingsdialog.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/ui/dialogs/settingsdialog.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/settingsdialog.cpp
CMakeFiles/QTFirewall.dir/src/ui/dialogs/settingsdialog.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building CXX object CMakeFiles/QTFirewall.dir/src/ui/dialogs/settingsdialog.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/ui/dialogs/settingsdialog.cpp.o -MF CMakeFiles/QTFirewall.dir/src/ui/dialogs/settingsdialog.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/ui/dialogs/settingsdialog.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/settingsdialog.cpp

CMakeFiles/QTFirewall.dir/src/ui/dialogs/settingsdialog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/ui/dialogs/settingsdialog.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/settingsdialog.cpp > CMakeFiles/QTFirewall.dir/src/ui/dialogs/settingsdialog.cpp.i

CMakeFiles/QTFirewall.dir/src/ui/dialogs/settingsdialog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/ui/dialogs/settingsdialog.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/settingsdialog.cpp -o CMakeFiles/QTFirewall.dir/src/ui/dialogs/settingsdialog.cpp.s

CMakeFiles/QTFirewall.dir/src/ui/dialogs/exportdialog.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/ui/dialogs/exportdialog.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/exportdialog.cpp
CMakeFiles/QTFirewall.dir/src/ui/dialogs/exportdialog.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building CXX object CMakeFiles/QTFirewall.dir/src/ui/dialogs/exportdialog.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/ui/dialogs/exportdialog.cpp.o -MF CMakeFiles/QTFirewall.dir/src/ui/dialogs/exportdialog.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/ui/dialogs/exportdialog.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/exportdialog.cpp

CMakeFiles/QTFirewall.dir/src/ui/dialogs/exportdialog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/ui/dialogs/exportdialog.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/exportdialog.cpp > CMakeFiles/QTFirewall.dir/src/ui/dialogs/exportdialog.cpp.i

CMakeFiles/QTFirewall.dir/src/ui/dialogs/exportdialog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/ui/dialogs/exportdialog.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/exportdialog.cpp -o CMakeFiles/QTFirewall.dir/src/ui/dialogs/exportdialog.cpp.s

CMakeFiles/QTFirewall.dir/src/ui/dialogs/aboutdialog.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/ui/dialogs/aboutdialog.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/aboutdialog.cpp
CMakeFiles/QTFirewall.dir/src/ui/dialogs/aboutdialog.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building CXX object CMakeFiles/QTFirewall.dir/src/ui/dialogs/aboutdialog.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/ui/dialogs/aboutdialog.cpp.o -MF CMakeFiles/QTFirewall.dir/src/ui/dialogs/aboutdialog.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/ui/dialogs/aboutdialog.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/aboutdialog.cpp

CMakeFiles/QTFirewall.dir/src/ui/dialogs/aboutdialog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/ui/dialogs/aboutdialog.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/aboutdialog.cpp > CMakeFiles/QTFirewall.dir/src/ui/dialogs/aboutdialog.cpp.i

CMakeFiles/QTFirewall.dir/src/ui/dialogs/aboutdialog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/ui/dialogs/aboutdialog.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/ui/dialogs/aboutdialog.cpp -o CMakeFiles/QTFirewall.dir/src/ui/dialogs/aboutdialog.cpp.s

CMakeFiles/QTFirewall.dir/src/utils/logger.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/utils/logger.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/utils/logger.cpp
CMakeFiles/QTFirewall.dir/src/utils/logger.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building CXX object CMakeFiles/QTFirewall.dir/src/utils/logger.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/utils/logger.cpp.o -MF CMakeFiles/QTFirewall.dir/src/utils/logger.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/utils/logger.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/utils/logger.cpp

CMakeFiles/QTFirewall.dir/src/utils/logger.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/utils/logger.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/utils/logger.cpp > CMakeFiles/QTFirewall.dir/src/utils/logger.cpp.i

CMakeFiles/QTFirewall.dir/src/utils/logger.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/utils/logger.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/utils/logger.cpp -o CMakeFiles/QTFirewall.dir/src/utils/logger.cpp.s

CMakeFiles/QTFirewall.dir/src/utils/configmanager.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/utils/configmanager.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/utils/configmanager.cpp
CMakeFiles/QTFirewall.dir/src/utils/configmanager.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Building CXX object CMakeFiles/QTFirewall.dir/src/utils/configmanager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/utils/configmanager.cpp.o -MF CMakeFiles/QTFirewall.dir/src/utils/configmanager.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/utils/configmanager.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/utils/configmanager.cpp

CMakeFiles/QTFirewall.dir/src/utils/configmanager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/utils/configmanager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/utils/configmanager.cpp > CMakeFiles/QTFirewall.dir/src/utils/configmanager.cpp.i

CMakeFiles/QTFirewall.dir/src/utils/configmanager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/utils/configmanager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/utils/configmanager.cpp -o CMakeFiles/QTFirewall.dir/src/utils/configmanager.cpp.s

CMakeFiles/QTFirewall.dir/src/utils/exportmanager.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/utils/exportmanager.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/utils/exportmanager.cpp
CMakeFiles/QTFirewall.dir/src/utils/exportmanager.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Building CXX object CMakeFiles/QTFirewall.dir/src/utils/exportmanager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/utils/exportmanager.cpp.o -MF CMakeFiles/QTFirewall.dir/src/utils/exportmanager.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/utils/exportmanager.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/utils/exportmanager.cpp

CMakeFiles/QTFirewall.dir/src/utils/exportmanager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/utils/exportmanager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/utils/exportmanager.cpp > CMakeFiles/QTFirewall.dir/src/utils/exportmanager.cpp.i

CMakeFiles/QTFirewall.dir/src/utils/exportmanager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/utils/exportmanager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/utils/exportmanager.cpp -o CMakeFiles/QTFirewall.dir/src/utils/exportmanager.cpp.s

CMakeFiles/QTFirewall.dir/src/utils/animationhelper.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/utils/animationhelper.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/utils/animationhelper.cpp
CMakeFiles/QTFirewall.dir/src/utils/animationhelper.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_39) "Building CXX object CMakeFiles/QTFirewall.dir/src/utils/animationhelper.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/utils/animationhelper.cpp.o -MF CMakeFiles/QTFirewall.dir/src/utils/animationhelper.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/utils/animationhelper.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/utils/animationhelper.cpp

CMakeFiles/QTFirewall.dir/src/utils/animationhelper.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/utils/animationhelper.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/utils/animationhelper.cpp > CMakeFiles/QTFirewall.dir/src/utils/animationhelper.cpp.i

CMakeFiles/QTFirewall.dir/src/utils/animationhelper.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/utils/animationhelper.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/utils/animationhelper.cpp -o CMakeFiles/QTFirewall.dir/src/utils/animationhelper.cpp.s

CMakeFiles/QTFirewall.dir/src/utils/thememanager.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/src/utils/thememanager.cpp.o: /home/<USER>/Desktop/dev/QT_Firewall/src/utils/thememanager.cpp
CMakeFiles/QTFirewall.dir/src/utils/thememanager.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_40) "Building CXX object CMakeFiles/QTFirewall.dir/src/utils/thememanager.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/src/utils/thememanager.cpp.o -MF CMakeFiles/QTFirewall.dir/src/utils/thememanager.cpp.o.d -o CMakeFiles/QTFirewall.dir/src/utils/thememanager.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/src/utils/thememanager.cpp

CMakeFiles/QTFirewall.dir/src/utils/thememanager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/src/utils/thememanager.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/src/utils/thememanager.cpp > CMakeFiles/QTFirewall.dir/src/utils/thememanager.cpp.i

CMakeFiles/QTFirewall.dir/src/utils/thememanager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/src/utils/thememanager.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/src/utils/thememanager.cpp -o CMakeFiles/QTFirewall.dir/src/utils/thememanager.cpp.s

CMakeFiles/QTFirewall.dir/QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp.o: QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp
CMakeFiles/QTFirewall.dir/QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_41) "Building CXX object CMakeFiles/QTFirewall.dir/QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp.o -MF CMakeFiles/QTFirewall.dir/QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp.o.d -o CMakeFiles/QTFirewall.dir/QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/build/QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp

CMakeFiles/QTFirewall.dir/QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/build/QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp > CMakeFiles/QTFirewall.dir/QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp.i

CMakeFiles/QTFirewall.dir/QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/build/QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp -o CMakeFiles/QTFirewall.dir/QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp.s

# Object files for target QTFirewall
QTFirewall_OBJECTS = \
"CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/main.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/core/application.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/core/applicationcontroller.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/models/packetmodel.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/models/rulemodel.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/models/alertmodel.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/models/networkinterfacemodel.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/models/configurationmodel.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/network/packetcapture.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/network/protocolanalyzer.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/network/ipanalyzer.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/network/arpanalyzer.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/network/tcpanalyzer.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/network/udpanalyzer.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/network/firewallengine.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/ui/mainwindow.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/ui/welcomewizard.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/ui/dashboardview.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/ui/rulemanagerview.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/ui/alertview.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/ui/reportview.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/ui/settingsview.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/ui/widgets/networkvisualizationwidget.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/ui/widgets/packetflowwidget.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/ui/widgets/rulebuilderwidget.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/ui/widgets/alertpanelwidget.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/ui/widgets/statisticswidget.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/ui/widgets/animatedbutton.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/ui/widgets/protocolfilterwidget.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/ui/dialogs/ruledialog.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/ui/dialogs/settingsdialog.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/ui/dialogs/exportdialog.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/ui/dialogs/aboutdialog.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/utils/logger.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/utils/configmanager.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/utils/exportmanager.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/utils/animationhelper.cpp.o" \
"CMakeFiles/QTFirewall.dir/src/utils/thememanager.cpp.o" \
"CMakeFiles/QTFirewall.dir/QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp.o"

# External object files for target QTFirewall
QTFirewall_EXTERNAL_OBJECTS =

QTFirewall: CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/main.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/core/application.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/core/applicationcontroller.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/models/packetmodel.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/models/rulemodel.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/models/alertmodel.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/models/networkinterfacemodel.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/models/configurationmodel.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/network/packetcapture.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/network/protocolanalyzer.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/network/ipanalyzer.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/network/arpanalyzer.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/network/tcpanalyzer.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/network/udpanalyzer.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/network/firewallengine.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/ui/mainwindow.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/ui/welcomewizard.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/ui/dashboardview.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/ui/rulemanagerview.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/ui/alertview.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/ui/reportview.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/ui/settingsview.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/ui/widgets/networkvisualizationwidget.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/ui/widgets/packetflowwidget.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/ui/widgets/rulebuilderwidget.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/ui/widgets/alertpanelwidget.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/ui/widgets/statisticswidget.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/ui/widgets/animatedbutton.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/ui/widgets/protocolfilterwidget.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/ui/dialogs/ruledialog.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/ui/dialogs/settingsdialog.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/ui/dialogs/exportdialog.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/ui/dialogs/aboutdialog.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/utils/logger.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/utils/configmanager.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/utils/exportmanager.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/utils/animationhelper.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/src/utils/thememanager.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/build.make
QTFirewall: /usr/lib/x86_64-linux-gnu/libQt6Network.so.6.4.2
QTFirewall: /usr/lib/x86_64-linux-gnu/libQt6Charts.so.6.4.2
QTFirewall: /usr/lib/x86_64-linux-gnu/libQt6Svg.so.6.4.2
QTFirewall: /usr/lib/x86_64-linux-gnu/libQt6Concurrent.so.6.4.2
QTFirewall: /usr/lib/x86_64-linux-gnu/libQt6PrintSupport.so.6.4.2
QTFirewall: /usr/lib/x86_64-linux-gnu/libQt6OpenGLWidgets.so.6.4.2
QTFirewall: /usr/lib/x86_64-linux-gnu/libQt6OpenGL.so.6.4.2
QTFirewall: /usr/lib/x86_64-linux-gnu/libQt6Widgets.so.6.4.2
QTFirewall: /usr/lib/x86_64-linux-gnu/libQt6Gui.so.6.4.2
QTFirewall: /usr/lib/x86_64-linux-gnu/libQt6Core.so.6.4.2
QTFirewall: /usr/lib/x86_64-linux-gnu/libGLX.so
QTFirewall: /usr/lib/x86_64-linux-gnu/libOpenGL.so
QTFirewall: CMakeFiles/QTFirewall.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_42) "Linking CXX executable QTFirewall"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/QTFirewall.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/QTFirewall.dir/build: QTFirewall
.PHONY : CMakeFiles/QTFirewall.dir/build

CMakeFiles/QTFirewall.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/QTFirewall.dir/cmake_clean.cmake
.PHONY : CMakeFiles/QTFirewall.dir/clean

CMakeFiles/QTFirewall.dir/depend: QTFirewall_autogen/3YJK5W5UP7/qrc_resources.cpp
	cd /home/<USER>/Desktop/dev/QT_Firewall/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/dev/QT_Firewall /home/<USER>/Desktop/dev/QT_Firewall /home/<USER>/Desktop/dev/QT_Firewall/build /home/<USER>/Desktop/dev/QT_Firewall/build /home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles/QTFirewall.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/QTFirewall.dir/depend

