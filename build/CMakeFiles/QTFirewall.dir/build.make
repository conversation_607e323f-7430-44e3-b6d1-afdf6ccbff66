# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/dev/QT_Firewall

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/dev/QT_Firewall/build

# Include any dependencies generated for this target.
include CMakeFiles/QTFirewall.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/QTFirewall.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/QTFirewall.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/QTFirewall.dir/flags.make

CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.o: CMakeFiles/QTFirewall.dir/flags.make
CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.o: QTFirewall_autogen/mocs_compilation.cpp
CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.o: CMakeFiles/QTFirewall.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.o"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.o -MF CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.o -c /home/<USER>/Desktop/dev/QT_Firewall/build/QTFirewall_autogen/mocs_compilation.cpp

CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.i"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/dev/QT_Firewall/build/QTFirewall_autogen/mocs_compilation.cpp > CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.i

CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.s"
	/usr/bin/g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/dev/QT_Firewall/build/QTFirewall_autogen/mocs_compilation.cpp -o CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.s

# Object files for target QTFirewall
QTFirewall_OBJECTS = \
"CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.o"

# External object files for target QTFirewall
QTFirewall_EXTERNAL_OBJECTS =

QTFirewall: CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.o
QTFirewall: CMakeFiles/QTFirewall.dir/build.make
QTFirewall: /usr/lib/x86_64-linux-gnu/libQt6Network.so.6.4.2
QTFirewall: /usr/lib/x86_64-linux-gnu/libQt6Charts.so.6.4.2
QTFirewall: /usr/lib/x86_64-linux-gnu/libQt6Svg.so.6.4.2
QTFirewall: /usr/lib/x86_64-linux-gnu/libQt6Concurrent.so.6.4.2
QTFirewall: /usr/lib/x86_64-linux-gnu/libQt6PrintSupport.so.6.4.2
QTFirewall: /usr/lib/x86_64-linux-gnu/libQt6OpenGLWidgets.so.6.4.2
QTFirewall: /usr/lib/x86_64-linux-gnu/libQt6OpenGL.so.6.4.2
QTFirewall: /usr/lib/x86_64-linux-gnu/libQt6Widgets.so.6.4.2
QTFirewall: /usr/lib/x86_64-linux-gnu/libQt6Gui.so.6.4.2
QTFirewall: /usr/lib/x86_64-linux-gnu/libQt6Core.so.6.4.2
QTFirewall: /usr/lib/x86_64-linux-gnu/libGLX.so
QTFirewall: /usr/lib/x86_64-linux-gnu/libOpenGL.so
QTFirewall: CMakeFiles/QTFirewall.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable QTFirewall"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/QTFirewall.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/QTFirewall.dir/build: QTFirewall
.PHONY : CMakeFiles/QTFirewall.dir/build

CMakeFiles/QTFirewall.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/QTFirewall.dir/cmake_clean.cmake
.PHONY : CMakeFiles/QTFirewall.dir/clean

CMakeFiles/QTFirewall.dir/depend:
	cd /home/<USER>/Desktop/dev/QT_Firewall/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/dev/QT_Firewall /home/<USER>/Desktop/dev/QT_Firewall /home/<USER>/Desktop/dev/QT_Firewall/build /home/<USER>/Desktop/dev/QT_Firewall/build /home/<USER>/Desktop/dev/QT_Firewall/build/CMakeFiles/QTFirewall.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/QTFirewall.dir/depend

