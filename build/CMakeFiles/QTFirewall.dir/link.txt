/usr/bin/g++ -g CMakeFiles/QTFirewall.dir/QTFirewall_autogen/mocs_compilation.cpp.o -o QTFirewall  /usr/lib/x86_64-linux-gnu/libQt6Network.so.6.4.2 /usr/lib/x86_64-linux-gnu/libQt6Charts.so.6.4.2 /usr/lib/x86_64-linux-gnu/libQt6Svg.so.6.4.2 /usr/lib/x86_64-linux-gnu/libQt6Concurrent.so.6.4.2 /usr/lib/x86_64-linux-gnu/libQt6PrintSupport.so.6.4.2 -lpcap /usr/lib/x86_64-linux-gnu/libQt6OpenGLWidgets.so.6.4.2 /usr/lib/x86_64-linux-gnu/libQt6OpenGL.so.6.4.2 /usr/lib/x86_64-linux-gnu/libQt6Widgets.so.6.4.2 /usr/lib/x86_64-linux-gnu/libQt6Gui.so.6.4.2 /usr/lib/x86_64-linux-gnu/libQt6Core.so.6.4.2 /usr/lib/x86_64-linux-gnu/libGLX.so /usr/lib/x86_64-linux-gnu/libOpenGL.so 
